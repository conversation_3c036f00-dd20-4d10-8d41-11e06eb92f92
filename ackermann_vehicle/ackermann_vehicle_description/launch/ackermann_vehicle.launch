<?xml version="1.0"?>

<!-- ackermann_vehicle.launch

Launch nodes used by both RViz and Gazebo when visualizing a vehicle with
Ackermann steering.

Copyright (c) 2013 Wunderkammer Laboratory

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Edit: 
for noetic this is the fix in this file at xacro search: 
https://answers.ros.org/question/366957/problem-with-xacro-invalid-param/
-->



<launch>
  <arg name="namespace" default="ackermann_vehicle"/>

  <group ns="$(arg namespace)">
    <!-- robot_description is used by nodes that publish to joint_states. -->
    <param name="robot_description"
           command="$(find xacro)/xacro '$(find ackermann_vehicle_description)/urdf/ackermann.urdf.xacro'"/>

    <!-- Read joint positions from joint_states, then publish the vehicle's
         state to tf. -->
    <node name="vehicle_state_publisher" pkg="robot_state_publisher"
          type="robot_state_publisher">
      <param name="publish_frequency" value="30.0"/>
    </node>
  </group>
</launch>
