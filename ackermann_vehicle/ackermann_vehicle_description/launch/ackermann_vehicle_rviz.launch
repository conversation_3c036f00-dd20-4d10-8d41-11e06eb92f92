<?xml version="1.0"?>

<!-- ackermann_vehicle_rviz.launch

Use RViz to visualize a vehicle with Ackermann steering.

Arguments:
    namespace (string, default: ackermann_vehicle)
        Vehicle namespace.

Copyright (c) 2013 Wunderkammer Laboratory

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<launch>
  <arg name="namespace" default="ackermann_vehicle"/>

  <include file="$(find ackermann_vehicle_description)/launch/ackermann_vehicle.launch">
    <arg name="namespace" value="$(arg namespace)"/>
  </include>

  <!-- Read joint positions from a GUI, then publish them to joint_states. -->
  <node name="joint_state_publisher" pkg="joint_state_publisher"
        type="joint_state_publisher" ns="$(arg namespace)">
    <param name="rate" value="30"/>
    <param name="use_gui" value="true"/>
  </node>

  <include file="$(find ackermann_vehicle_description)/launch/rviz.launch">
    <arg name="namespace" value="$(arg namespace)"/>
  </include>
</launch>
