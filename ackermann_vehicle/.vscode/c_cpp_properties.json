{"configurations": [{"browse": {"databaseFilename": "", "limitSymbolsToIncludedHeaders": true}, "includePath": ["/home/<USER>/ugv_ws/devel/include/**", "/home/<USER>/catkin_ws/devel/include/**", "/opt/ros/melodic/include/**", "/home/<USER>/catkin_ws/src/navigation/amcl/include/**", "/home/<USER>/catkin_ws/src/navigation/base_local_planner/include/**", "/home/<USER>/catkin_ws/src/navigation/carrot_planner/include/**", "/home/<USER>/catkin_ws/src/navigation/clear_costmap_recovery/include/**", "/home/<USER>/catkin_ws/src/navigation/costmap_2d/include/**", "/home/<USER>/catkin_ws/src/navigation/dwa_local_planner/include/**", "/home/<USER>/ugv_ws/src/DynamixelSDK/ros/include/**", "/home/<USER>/ugv_ws/src/dynamixel-workbench/dynamixel_workbench_controllers/include/**", "/home/<USER>/ugv_ws/src/dynamixel-workbench/dynamixel_workbench_operators/include/**", "/home/<USER>/ugv_ws/src/dynamixel-workbench/dynamixel_workbench_toolbox/include/**", "/home/<USER>/catkin_ws/src/navigation/global_planner/include/**", "/home/<USER>/ugv_ws/src/mavros/libmavconn/include/**", "/home/<USER>/catkin_ws/src/navigation/map_server/include/**", "/home/<USER>/ugv_ws/src/mavros/mavros/include/**", "/home/<USER>/ugv_ws/src/mavros/mavros_msgs/include/**", "/home/<USER>/catkin_ws/src/navigation/move_base/include/**", "/home/<USER>/catkin_ws/src/navigation/move_slow_and_clear/include/**", "/home/<USER>/catkin_ws/src/navigation/nav_core/include/**", "/home/<USER>/catkin_ws/src/navigation/navfn/include/**", "/home/<USER>/catkin_ws/src/navigation/rotate_recovery/include/**", "/home/<USER>/catkin_ws/src/teb_local_planner/include/**", "/home/<USER>/ugv_ws/src/mavros/test_mavros/include/**", "/home/<USER>/catkin_ws/src/turtlebot3_simulations/turtlebot3_fake/include/**", "/home/<USER>/catkin_ws/src/turtlebot3_simulations/turtlebot3_gazebo/include/**", "/home/<USER>/catkin_ws/src/turtlebot3/turtlebot3_slam/include/**", "/home/<USER>/ugv_ws/src/ugv_drive/include/**", "/home/<USER>/ugv_ws/src/velodyne/velodyne_driver/include/**", "/home/<USER>/ugv_ws/src/velodyne/velodyne_laserscan/include/**", "/home/<USER>/ugv_ws/src/velodyne/velodyne_pointcloud/include/**", "/home/<USER>/catkin_ws/src/navigation/voxel_grid/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "c11", "cppStandard": "c++17"}], "version": 4}