# em_3905_joint_ctrlr_params.yaml
#
# This file defines joint controller parameter values used for simulating a
# Traxxas(R) E-Maxx(R) #3905 RC (Radio Controlled) truck.
#
# Traxxas(R) and E-Maxx(R) are registered trademarks of Traxxas Management,
# LLC. em_3905_joint_ctrlr_params.yaml was independently created by
# Wunderkammer Laboratory, and neither em_3905_ros_ctrlr_params.yaml nor
# Wunderkammer Laboratory is affiliated with, sponsored by, approved by, or
# endorsed by Traxxas Management, LLC. All other trademarks and service marks
# are the property of their respective owners.
#
# Copyright (c) 2013-2014 Wunderkammer Laboratory
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Publish the joint states to joint_states.
joint_state_ctrlr:
  type: joint_state_controller/JointStateController
  publish_rate: 30

left_steering_ctrlr:
  joint: left_steering_joint
  type: effort_controllers/JointPositionController
  pid: {p: 4.0, i: 0.0, d: 1.0}
right_steering_ctrlr:
  joint: right_steering_joint
  type: effort_controllers/JointPositionController
  pid: {p: 4.0, i: 0.0, d: 1.0}

left_front_axle_ctrlr:
  joint: left_front_axle
  type: effort_controllers/JointVelocityController
  pid: {p: 1.5, i: 1.0, d: 0.0, i_clamp: 10.0}
right_front_axle_ctrlr:
  joint: right_front_axle
  type: effort_controllers/JointVelocityController
  pid: {p: 1.5, i: 1.0, d: 0.0, i_clamp: 10.0}
left_rear_axle_ctrlr:
  joint: left_rear_axle
  type: effort_controllers/JointVelocityController
  pid: {p: 1.5, i: 1.0, d: 0.0, i_clamp: 10.0}
right_rear_axle_ctrlr:
  joint: right_rear_axle
  type: effort_controllers/JointVelocityController
  pid: {p: 1.5, i: 1.0, d: 0.0, i_clamp: 10.0}

# The proportional gain of each shock absorber controller is
# 2 * (shock_stroke * shock_spring_constant / wheel_travel). shock_stroke is
# 0.028575 m. shock_spring_constant, an approximation of a Traxxas Ultra Shock
# shock absorber spring's constant, is 437.817 N/m. wheel_travel, defined in
# em_3905.urdf.xacro, is 0.084 m.
#
# The derivative gain of each shock absorber controller is 2 * shock_damping.
# shock_damping, an approximation of the viscous damping coefficient of an
# Ultra Shock shock absorber, is 36.2416 Ns/m
# (i.e. sqrt(3 * shock_spring_constant)).

  # left_front_shock_ctrlr:
  #   joint: left_front_shock
  #   type: effort_controllers/JointPositionController
  #   pid: {p: 297.8719, i: 0.0, d: 72.4832}
  # right_front_shock_ctrlr:
  #   joint: right_front_shock
  #   type: effort_controllers/JointPositionController
  #   pid: {p: 297.8719, i: 0.0, d: 72.4832}
  # left_rear_shock_ctrlr:
  #   joint: left_rear_shock
  #   type: effort_controllers/JointPositionController
  #   pid: {p: 297.8719, i: 0.0, d: 72.4832}
  # right_rear_shock_ctrlr:
  #   joint: right_rear_shock
  #   type: effort_controllers/JointPositionController
  #   pid: {p: 297.8719, i: 0.0, d: 72.4832}
left_front_shock_ctrlr:
  joint: left_front_shock
  type: effort_controllers/JointPositionController
  pid: {p: 1, i: 0.0, d: 1}
right_front_shock_ctrlr:
  joint: right_front_shock
  type: effort_controllers/JointPositionController
  pid: {p: 1, i: 0.0, d: 1}
left_rear_shock_ctrlr:
  joint: left_rear_shock
  type: effort_controllers/JointPositionController
  pid: {p: 1, i: 0.0, d: 1}
right_rear_shock_ctrlr:
  joint: right_rear_shock
  type: effort_controllers/JointPositionController
  pid: {p: 1, i: 0.0, d: 1}
