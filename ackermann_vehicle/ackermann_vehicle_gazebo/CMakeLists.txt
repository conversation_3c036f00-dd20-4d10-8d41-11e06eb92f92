cmake_minimum_required(VERSION 3.0.2)
project(ackermann_vehicle_gazebo)

# Menemukan paket Catkin dan komponen yang diperlukan untuk C++
find_package(catkin REQUIRED COMPONENTS
  roscpp
  sensor_msgs
  geometry_msgs
)

# Menambahkan direktori include untuk header files
# Ini akan menyelesaikan error "ros/ros.h: No such file or directory"
include_directories(
  include
  ${catkin_INCLUDE_DIRS}
)

# De<PERSON>rasi paket Catkin
catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES ${PROJECT_NAME}
#  CATKIN_DEPENDS roscpp sensor_msgs geometry_msgs
#  DEPENDS system_lib
)

# Instalasi skrip Python (jika masih diperlukan)
catkin_install_python(PROGRAMS
  scripts/ackermann_controller
  # scripts/simple_obstacle_avoider.py # Komentari jika tidak digunakan lagi
  DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

# Instalasi direktori launch dan config
install(DIRECTORY launch/ config/ worlds/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)

# Membuat executable untuk node C++
add_executable(${PROJECT_NAME}_node src/simple_obstacle_avoider.cpp)

# Menautkan library yang diperlukan ke executable
target_link_libraries(${PROJECT_NAME}_node
  ${catkin_LIBRARIES}
)