#include "ros/ros.h"
#include "sensor_msgs/LaserScan.h"
#include "geometry_msgs/Twist.h"
#include <limits> // Diperlukan untuk std::numeric_limits

class ObstacleAvoider {
public:
    ObstacleAvoider() {
        // Inisialisasi NodeHandle
        nh_ = ros::NodeHandle("~"); // Menggunakan private NodeHandle

        // Publisher untuk mengirim perintah kecepatan
        cmd_vel_pub_ = nh_.advertise<geometry_msgs::Twist>("/cmd_vel", 10);

        // Subscriber untuk data LIDAR
        scan_sub_ = nh_.subscribe("/scan", 1, &ObstacleAvoider::scanCallback, this);

        // Jarak ambang batas untuk berhenti (dalam meter)
        stop_distance_ = 1.0;

        ROS_INFO("Node Pengecut (Obstacle Avoider) C++ aktif.");
    }

    void scanCallback(const sensor_msgs::LaserScan::ConstPtr& scan_data) {
        // Ambil beberapa titik di tengah-tengah data scan untuk representasi "depan"
        size_t num_readings = scan_data->ranges.size();
        if (num_readings == 0) {
            return;
        }

        int front_arc_start_idx = (num_readings / 2) - 15;
        int front_arc_end_idx = (num_readings / 2) + 15;

        // Pastikan indeks tidak keluar dari batas
        if (front_arc_start_idx < 0) front_arc_start_idx = 0;
        if (front_arc_end_idx >= num_readings) front_arc_end_idx = num_readings - 1;

        // Cari jarak minimum di area depan, abaikan nilai 0 atau tak terhingga
        float min_distance = std::numeric_limits<float>::infinity();
        for (int i = front_arc_start_idx; i <= front_arc_end_idx; ++i) {
            if (scan_data->ranges[i] > 0 && scan_data->ranges[i] < min_distance) {
                min_distance = scan_data->ranges[i];
            }
        }

        // Logika utama: jika jarak minimum kurang dari ambang batas, berhenti.
        if (min_distance < stop_distance_) {
            ROS_WARN("Rintangan terdeteksi di depan! Berhenti. Jarak: %f m", min_distance);
            stopRobot();
        }
    }

    void stopRobot() {
        // Mempublikasikan perintah Twist dengan semua nilai nol untuk berhenti.
        geometry_msgs::Twist stop_cmd;
        // Nilai linear dan angular secara default adalah 0.0
        cmd_vel_pub_.publish(stop_cmd);
    }

    void run() {
        // Menjaga node tetap berjalan dan memproses callback
        ros::spin();
    }

private:
    ros::NodeHandle nh_;
    ros::Publisher cmd_vel_pub_;
    ros::Subscriber scan_sub_;
    double stop_distance_;
};

int main(int argc, char **argv) {
    // Inisialisasi node ROS
    ros::init(argc, argv, "obstacle_avoider_node_cpp");

    try {
        ObstacleAvoider avoider;
        avoider.run();
    } catch (const ros::Exception& e) {
        ROS_ERROR("Terjadi exception: %s", e.what());
    }

    return 0;
}
