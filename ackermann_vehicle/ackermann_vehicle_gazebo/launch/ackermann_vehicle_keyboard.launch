<?xml version="1.0"?>
<launch>
  <!-- 
    This launch file starts the Ackermann vehicle simulation in Gazebo
    and launches nodes for keyboard control.
  -->

  <!-- 1. Start the main Ackermann vehicle simulation -->
  <!-- 2. Start the converter from /cmd_vel to /ackermann_cmd -->
  <!-- This node listens to standard Twist messages and converts them to AckermannDriveStamped messages -->
  <node pkg="ackermann_vehicle_navigation" type="cmd_vel_to_ackermann_drive.py" name="cmd_vel_to_ackermann_drive" output="screen"/>

  <!-- 3. Start the keyboard teleoperation node -->
  <!-- This node captures keyboard input and publishes it as Twist messages on /cmd_vel.
       The 'launch-prefix' opens a new terminal window for this node, which is necessary
       for it to capture keystrokes correctly. -->
  <node pkg="teleop_twist_keyboard" type="teleop_twist_keyboard.py" name="teleop_twist_keyboard" output="screen" launch-prefix="gnome-terminal --"/>
</launch>
