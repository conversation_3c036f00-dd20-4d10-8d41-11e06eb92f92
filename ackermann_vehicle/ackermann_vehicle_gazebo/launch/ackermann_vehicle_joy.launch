<?xml version="1.0"?>

<!-- ackermann_vehicle.launch

Use Gazebo to simulate a vehicle with Ackermann steering.

Arguments:
    namespace (string, default: ackermann_vehicle)
        Vehicle namespace.
    world_name (string, default: worlds/empty.world)
        Gazebo world name.
    cmd_timeout (double, default: 0.5)
        Command timeout passed to the ackermann_controller node.
    x (double, default: 0.0)
    y (double, default: 0.0)
    z (double, default: 0.1)
    roll (double, default: 0.0)
    pitch (double, default: 0.0)
    yaw (double, default: 0.0)
        Vehicle pose. x, y, and z are measured in meters. roll, pitch, and yaw
        are measured in radians.

Copyright (c) 2013-2015 Wunderkammer Laboratory

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<launch>
  <include file="$(find ackermann_vehicle_gazebo)/launch/ackermann_vehicle_hokuyo_imu.launch"/>
  <include file="$(find teleop_twist_joy)/launch/teleop.launch"/>
  <node pkg="ackermann_vehicle_gazebo" type="cmd_vel_to_ackermann_drive.py" name="cmd_vel_to_ackermann_drive" />
</launch>
