<?xml version="1.0"?>
<package>
  <name>ackermann_vehicle_gazebo</name>
  <version>0.1.3</version>
  <description>
    ackermann_vehicle_gazebo is used with Gazebo to simulate a vehicle with
    Ackermann steering.
  </description>
  <!--
    Copyright (c) 2013-2015 Wunderkammer Laboratory

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
  -->
  <maintainer email="<EMAIL>">
    <PERSON>
  </maintainer>
  <license>Apache 2.0</license>

  <url type="website">http://wiki.ros.org/ackermann_vehicle_gazebo</url>
  <url type="bugtracker">https://github.com/wunderkammer-laboratory/ackermann_vehicle/issues</url>
  <url type="repository">https://github.com/wunderkammer-laboratory/ackermann_vehicle.git</url>

  <author email="<EMAIL>">Jim <PERSON>rock</author>

  <buildtool_depend>catkin</buildtool_depend>

  <run_depend>ackermann_msgs</run_depend>
  <run_depend>ackermann_vehicle_description</run_depend>
  <run_depend>controller_manager</run_depend>
  <run_depend>controller_manager_msgs</run_depend>
  <run_depend>effort_controllers</run_depend>
  <run_depend>gazebo_ros</run_depend>
  <run_depend>joint_state_controller</run_depend>
  <run_depend>rospy</run_depend>
  <run_depend>std_msgs</run_depend>
  <run_depend>tf</run_depend>
  <run_depend>joy</run_depend>
  <run_depend>teleop_twist_joy</run_depend>

  <export>
    <architecture_independent/>
    <rosdoc config="rosdoc.yaml"/>
  </export>
</package>
