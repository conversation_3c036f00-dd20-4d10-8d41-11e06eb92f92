<sdf version='1.6'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose frame=''>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <surface>
            <contact>
              <collide_bitmask>65535</collide_bitmask>
              <ode/>
            </contact>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <gravity>0 0 -9.8</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <physics name='default_physics' default='0' type='ode'>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <audio>
      <device>default</device>
    </audio>
    <wind/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='unit_box'>
      <pose frame=''>0.590738 0.60469 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1.20126 1.28837 2.00595</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1.20126 1.28837 2.00595</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone'>
      <pose frame=''>0.5497 1.49077 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_0'>
      <pose frame=''>0.542038 3.47351 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_1'>
      <pose frame=''>0.55967 5.47509 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_2'>
      <pose frame=''>0.545616 7.45285 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_3'>
      <pose frame=''>0.543198 6.45342 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_4'>
      <pose frame=''>-2.47532 7.50096 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_5'>
      <pose frame=''>-2.47617 5.47727 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_7'>
      <pose frame=''>-2.47412 3.51977 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_8'>
      <pose frame=''>-2.50304 1.43642 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_9'>
      <pose frame=''>-2.48784 2.49434 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_10'>
      <pose frame=''>-3.45923 0.496209 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_11'>
      <pose frame=''>-6.40158 0.469192 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_12'>
      <pose frame=''>-6.48918 1.4437 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_13'>
      <pose frame=''>-6.43074 2.53456 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_14'>
      <pose frame=''>-6.48652 3.48844 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_15'>
      <pose frame=''>-6.34699 5.29292 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_16'>
      <pose frame=''>-6.34299 6.55486 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_17'>
      <pose frame=''>-6.73169 7.69456 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_19'>
      <pose frame=''>-2.42105 6.48001 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_21'>
      <pose frame=''>-3.51197 5.67389 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22'>
      <pose frame=''>-3.33477 7.37903 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='building_template_model'>
      <pose frame=''>-2.98967 4.02339 0 0 -0 0</pose>
      <link name='Wall_0'>
        <collision name='Wall_0_Collision'>
          <geometry>
            <box>
              <size>8.48924 0.15 2.5</size>
            </box>
          </geometry>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_0_Visual'>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.48924 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose frame=''>-4.55 0.007581 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_1'>
        <collision name='Wall_1_Collision'>
          <geometry>
            <box>
              <size>9.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_1_Visual'>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>9.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose frame=''>0 4.15427 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_4'>
        <collision name='Wall_4_Collision'>
          <geometry>
            <box>
              <size>8.5044 0.15 2.5</size>
            </box>
          </geometry>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_4_Visual'>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.5044 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose frame=''>4.55 -0 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <static>1</static>
    </model>
    <state world_name='default'>
      <sim_time>612 781000000</sim_time>
      <real_time>704 629179772</real_time>
      <wall_time>1593492542 262126125</wall_time>
      <iterations>420782</iterations>
      <model name='ackermann_vehicle'>
        <pose frame=''>-1.04043 -1.78553 0.104226 8.1e-05 -0.000179 1.54833</pose>
        <scale>1 1 1</scale>
        <link name='base_link'>
          <pose frame=''>-1.03104 -1.8347 0.105082 5.1e-05 -0.007693 1.57911</pose>
          <velocity>-0.000173 2.6e-05 0.000129 -0.000487 0.078 0.001524</velocity>
          <acceleration>0.465086 0.001248 0.256231 -0.327552 -0.650864 -2.75265</acceleration>
          <wrench>1.89104 0.005074 1.04183 0 -0 0</wrench>
        </link>
        <link name='left_front_axle_carrier'>
          <pose frame=''>-1.18492 -1.66822 0.073023 5.1e-05 -0.007693 1.57914</pose>
          <velocity>0.007347 0.000157 -0.003449 -0.000483 0.077704 0.002064</velocity>
          <acceleration>4.84683 0.137555 -6.69359 -0.127228 1.19907 1.10925</acceleration>
          <wrench>0.004847 0.000138 -0.006694 0 -0 0</wrench>
        </link>
        <link name='left_front_wheel'>
          <pose frame=''>-1.18493 -1.66822 0.073023 1.57085 0.005592 1.57914</pose>
          <velocity>0.005583 0.000109 -0.003448 -0.000218 0.076494 0.0021</velocity>
          <acceleration>3.69069 0.051478 -6.69166 -3.10023 0.328945 1.60359</acceleration>
          <wrench>1.0703 0.014929 -1.94058 0 -0 0</wrench>
        </link>
        <link name='left_rear_axle_carrier'>
          <pose frame=''>-1.18214 -2.00321 0.073023 5.1e-05 -0.007693 1.57911</pose>
          <velocity>0.008605 0.000232 -0.003399 -0.000478 0.077303 0.002347</velocity>
          <acceleration>5.489 0.24484 -7.06266 -0.123238 1.07198 -2.85066</acceleration>
          <wrench>0.005489 0.000245 -0.007063 0 -0 0</wrench>
        </link>
        <link name='left_rear_wheel'>
          <pose frame=''>-1.18214 -2.00321 0.073023 -1.57102 1.36726 -1.56271</pose>
          <velocity>0.005423 0.00014 -0.003398 -0.000576 0.074339 0.002206</velocity>
          <acceleration>3.6295 0.080982 -7.06019 -0.769392 -0.518208 -1.20866</acceleration>
          <wrench>1.05255 0.023485 -2.04745 0 -0 0</wrench>
        </link>
        <link name='left_steering_link'>
          <pose frame=''>-1.18493 -1.66822 0.073023 5.1e-05 -0.007693 1.57911</pose>
          <velocity>0.003137 4.3e-05 -0.003453 -0.000484 0.077862 0.001029</velocity>
          <acceleration>2.07698 -0.066156 -6.70219 -0.12814 1.30493 2.61606</acceleration>
          <wrench>0.002077 -6.6e-05 -0.006702 0 -0 0</wrench>
        </link>
        <link name='right_front_axle_carrier'>
          <pose frame=''>-0.879936 -1.66569 0.073023 5.2e-05 -0.007693 1.57907</pose>
          <velocity>0.007369 3.2e-05 -0.003449 -0.000489 0.077826 -7.6e-05</velocity>
          <acceleration>4.89069 -0.043348 -7.23118 -0.140169 1.35006 -0.370063</acceleration>
          <wrench>0.004891 -4.3e-05 -0.007231 0 -0 0</wrench>
        </link>
        <link name='right_front_wheel'>
          <pose frame=''>-0.879937 -1.66569 0.073023 -1.57085 -0.278688 -1.5625</pose>
          <velocity>0.005598 5.9e-05 -0.003447 -0.000844 0.076676 -5.5e-05</velocity>
          <acceleration>3.71687 0.043104 -7.22779 0.838894 0.6626 -0.516654</acceleration>
          <wrench>1.07789 0.0125 -2.09606 0 -0 0</wrench>
        </link>
        <link name='right_rear_axle_carrier'>
          <pose frame=''>-0.87716 -2.00068 0.073022 5.1e-05 -0.007693 1.57911</pose>
          <velocity>-0.005686 0.000208 -0.003403 -0.000492 0.078028 0.0024</velocity>
          <acceleration>-2.8461 0.571847 -6.53095 -0.143905 1.43935 -2.84772</acceleration>
          <wrench>-0.002846 0.000572 -0.006531 0 -0 0</wrench>
        </link>
        <link name='right_rear_wheel'>
          <pose frame=''>-0.877157 -2.00068 0.073023 1.57087 0.77067 1.57916</pose>
          <velocity>0.00544 5.3e-05 -0.003403 0.000579 0.07456 0.002146</velocity>
          <acceleration>3.6492 0.065107 -6.53144 -2.42844 -0.265948 -1.06491</acceleration>
          <wrench>1.05827 0.018881 -1.89412 0 -0 0</wrench>
        </link>
        <link name='right_steering_link'>
          <pose frame=''>-0.879939 -1.66569 0.073023 5.1e-05 -0.007693 1.5791</pose>
          <velocity>0.003149 9.7e-05 -0.003452 -0.00049 0.077956 0.001003</velocity>
          <acceleration>2.10084 0.160649 -7.23704 -0.140704 1.4103 2.60151</acceleration>
          <wrench>0.002101 0.000161 -0.007237 0 -0 0</wrench>
        </link>
        <!-- <link name='base_link'>
          <pose frame=''>-1.04043 -1.78553 0.104226 8.1e-05 -0.000179 1.54833</pose>
          <velocity>0.000258 -0.001216 0.000274 0.00913 0.003847 -9.1e-05</velocity>
          <acceleration>0.024191 0.02818 0.082927 -1.98838 0.309514 0.308787</acceleration>
          <wrench>0.098361 0.114578 0.337181 0 -0 0</wrench>
        </link>
        <link name='left_front_axle_carrier'>
          <pose frame=''>-1.1891 -1.61462 0.072985 -6.6e-05 -0.000184 2.33372</pose>
          <velocity>-0.000546 -0.002037 -0.042647 0.009839 0.003088 -0.000283</velocity>
          <acceleration>-1.39015 -1.11597 -52.645 0.464495 -1.13241 0.171665</acceleration>
          <wrench>-0.00139 -0.001116 -0.052645 0 -0 0</wrench>
        </link>
        <link name='left_front_wheel'>
          <pose frame=''>-1.18911 -1.61463 0.072985 1.57073 0.374634 2.3337</pose>
          <velocity>-0.000386 -0.0018 -0.042651 -0.000698 -0.008783 -0.000291</velocity>
          <acceleration>-1.22355 -0.876161 -52.6446 -0.776108 -1.39447 -0.345553</acceleration>
          <wrench>-0.354829 -0.254087 -15.2669 0 -0 0</wrench>
        </link>
        <link name='left_rear_axle_carrier'>
          <pose frame=''>-1.19662 -1.94956 0.073027 6.7e-05 -0.000179 1.54833</pose>
          <velocity>0.000692 -0.000536 0.003239 0.009154 0.006219 -8.9e-05</velocity>
          <acceleration>0.364799 1.15756 3.39082 -0.564516 -1.11563 0.357631</acceleration>
          <wrench>0.000365 0.001158 0.003391 0 -0 0</wrench>
        </link>
        <link name='left_rear_wheel'>
          <pose frame=''>-1.19663 -1.94956 0.073027 -1.57086 0.886423 -1.59332</pose>
          <velocity>0.00057 -0.000649 0.003241 0.008967 0.007873 -0.000136</velocity>
          <acceleration>0.26772 0.932257 3.39577 2.97627 -0.703363 -2.42994</acceleration>
          <wrench>0.077639 0.270355 0.984772 0 -0 0</wrench>
        </link>
        <link name='left_steering_link'>
          <pose frame=''>-1.18911 -1.61463 0.072985 8.3e-05 -0.000178 1.54832</pose>
          <velocity>-0.000279 -0.001587 -0.042635 0.009579 0.003187 -2e-06</velocity>
          <acceleration>-0.928599 -0.48947 -52.6458 0.109004 -0.741376 0.439439</acceleration>
          <wrench>-0.000929 -0.000489 -0.052646 0 -0 0</wrench>
        </link>
        <link name='right_front_axle_carrier'>
          <pose frame=''>-0.88424 -1.6215 0.073025 -1.4e-05 -0.000194 2.16206</pose>
          <velocity>0.000688 -0.000697 0.00998 0.007313 0.006404 8e-06</velocity>
          <acceleration>-1.4306 0.565778 50.9577 1.90231 -0.547662 -2.21209</acceleration>
          <wrench>-0.001431 0.000566 0.050958 0 -0 0</wrench>
        </link>
        <link name='right_front_wheel'>
          <pose frame=''>-0.884234 -1.6215 0.073025 -1.5708 -0.654022 -0.979527</pose>
          <velocity>0.000648 -0.000702 0.009981 0.00961 0.008875 1e-06</velocity>
          <acceleration>-1.27373 0.533868 50.9578 1.65222 1.01232 -0.198051</acceleration>
          <wrench>-0.369383 0.154822 14.7778 0 -0 0</wrench>
        </link>
        <link name='right_rear_axle_carrier'>
          <pose frame=''>-0.891726 -1.95641 0.072983 8.6e-05 -0.000179 1.54832</pose>
          <velocity>-0.000513 -0.000868 -0.04443 0.009122 0.00271 8.7e-05</velocity>
          <acceleration>-0.755318 0.507744 -45.2722 -0.429537 0.049484 0.520193</acceleration>
          <wrench>-0.000755 0.000508 -0.045272 0 -0 0</wrench>
        </link>
        <link name='right_rear_wheel'>
          <pose frame=''>-0.891742 -1.95641 0.072983 1.57101 1.29607 1.54853</pose>
          <velocity>0.001722 -0.001124 -0.044433 0.000744 0.0097 -0.000612</velocity>
          <acceleration>1.3179 0.500482 -45.2719 -0.342867 -1.07404 -2.01838</acceleration>
          <wrench>0.382192 0.14514 -13.1288 0 -0 0</wrench>
        </link>
        <link name='right_steering_link'>
          <pose frame=''>-0.884225 -1.6215 0.073025 9.3e-05 -0.000172 1.54833</pose>
          <velocity>0.000461 -0.000804 0.009979 0.008046 0.005142 -0.000109</velocity>
          <acceleration>-0.930807 0.549409 50.957 0.357868 -0.251903 -2.71048</acceleration>
          <wrench>-0.000931 0.000549 0.050957 0 -0 0</wrench>
        </link> -->
      </model>
      <model name='building_template_model'>
        <pose frame=''>-2.98967 4.02339 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='Wall_0'>
          <pose frame=''>-7.53967 4.03097 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_1'>
          <pose frame=''>-2.98967 8.17766 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_4'>
          <pose frame=''>1.56033 4.02339 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='ground_plane'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box'>
        <pose frame=''>0.518834 0.484919 0.249258 0 -0 1e-06</pose>
        <scale>0.832461 0.776172 0.498517</scale>
        <link name='link'>
          <pose frame=''>0.518834 0.484919 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone'>
        <pose frame=''>0.5497 1.49077 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.5497 1.49077 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_0'>
        <pose frame=''>0.542038 3.47351 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.542038 3.47351 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_1'>
        <pose frame=''>0.55967 5.47509 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.55967 5.47509 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_10'>
        <pose frame=''>-3.41672 0.646729 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.41672 0.646729 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_11'>
        <pose frame=''>-4.43155 0.580733 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-4.43155 0.580733 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_12'>
        <pose frame=''>-4.46964 1.50657 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-4.46964 1.50657 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_13'>
        <pose frame=''>-5.47189 7.54665 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-5.47189 7.54665 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_14'>
        <pose frame=''>-2.41544 0.611316 0.249258 0 -0 0.000133</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.41544 0.611316 0.249258 0 -0 0.000133</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_15'>
        <pose frame=''>-5.4408 4.49737 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-5.4408 4.49737 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_16'>
        <pose frame=''>-5.4334 1.46768 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-5.4334 1.46768 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_17'>
        <pose frame=''>-6.48562 7.5467 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.48562 7.5467 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_19'>
        <pose frame=''>-1.45127 5.51659 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-1.45127 5.51659 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_2'>
        <pose frame=''>-2.41813 7.52932 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.41813 7.52932 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_21'>
        <pose frame=''>-4.46646 5.50761 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-4.46646 5.50761 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -0 0 -0 0</acceleration>
          <wrench>0 0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22'>
        <pose frame=''>-1.47495 0.601191 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-1.47495 0.601191 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone'>
        <pose frame=''>-3.51782 7.53227 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.51782 7.53227 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone_0'>
        <pose frame=''>-3.4644 4.45117 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.4644 4.45117 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone_1'>
        <pose frame=''>-5.43751 5.473 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-5.43751 5.473 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone_2'>
        <pose frame=''>0.520277 2.49449 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.520277 2.49449 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone_3'>
        <pose frame=''>-4.50332 7.52217 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-4.50332 7.52217 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_3'>
        <pose frame=''>0.543198 6.45342 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.543198 6.45342 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_4'>
        <pose frame=''>-1.50966 1.46847 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-1.50966 1.46847 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_5'>
        <pose frame=''>-2.45495 5.46685 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.45495 5.46685 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 -0 0 0 -0 0</acceleration>
          <wrench>-0 -0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_7'>
        <pose frame=''>-1.53243 4.48955 0.249258 0 -0 1.7e-05</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-1.53243 4.48955 0.249258 0 -0 1.7e-05</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -0 0 -0 0</acceleration>
          <wrench>0 0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_8'>
        <pose frame=''>-2.39586 1.50119 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.39586 1.50119 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_9'>
        <pose frame=''>-4.44943 4.48536 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-4.44943 4.48536 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <light name='sun'>
        <pose frame=''>0 0 10 0 -0 0</pose>
      </light>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose frame=''>-2.66689 4.36504 18.6658 0 1.5698 -3.13818</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
    <model name='ackermann_vehicle'>
      <link name='base_link'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <inertial>
          <pose frame=''>-0.006192 0 0.004005 0 -0 0</pose>
          <mass>4.066</mass>
          <inertia>
            <ixx>0.0157528</ixx>
            <ixy>0</ixy>
            <ixz>-8.77514e-05</ixz>
            <iyy>0.023805</iyy>
            <iyz>0</iyz>
            <izz>0.0303749</izz>
          </inertia>
        </inertial>
        <collision name='base_link_fixed_joint_lump__chassis_collision'>
          <pose frame=''>0 0 -0.005 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.258 0.168 0.01</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <collision name='base_link_fixed_joint_lump__left_battery_collision_1'>
          <pose frame=''>-0.025 0.057319 0.00052 0.349066 -0 0</pose>
          <geometry>
            <box>
              <size>0.16 0.047 0.024</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <collision name='base_link_fixed_joint_lump__right_battery_collision_2'>
          <pose frame=''>-0.025 -0.057319 0.00052 -0.349066 0 0</pose>
          <geometry>
            <box>
              <size>0.16 0.047 0.024</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <collision name='base_link_fixed_joint_lump__hokuyo_laser_frame_collision_3'>
          <pose frame=''>0 0 0.0985 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.058 0.058 0.087</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='base_link_visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.01 0.01 0.01</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__chassis_visual_1'>
          <pose frame=''>0 0 -0.005 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.258 0.168 0.01</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__left_battery_visual_2'>
          <pose frame=''>-0.025 0.057319 0.00052 0.349066 -0 0</pose>
          <geometry>
            <box>
              <size>0.16 0.047 0.024</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Blue</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__right_battery_visual_3'>
          <pose frame=''>-0.025 -0.057319 0.00052 -0.349066 0 0</pose>
          <geometry>
            <box>
              <size>0.16 0.047 0.024</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Blue</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__hokuyo_laser_frame_visual_4'>
          <pose frame=''>0 0 0.11 0 -0 0</pose>
          <geometry>
            <mesh>
              <scale>1 1 1</scale>
              <uri>/home/<USER>/catkin_ws/src/hector_models/hector_sensors_description/meshes/hokuyo_utm30lx/hokuyo_utm_30lx.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>__default__</uri>
              <name>__default__</name>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__imu_link_visual_5'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.02 0.035 0.002</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>__default__</uri>
              <name>__default__</name>
            </script>
          </material>
        </visual>
        <velocity_decay/>
        <velocity_decay/>
        <velocity_decay/>
        <velocity_decay/>
        <velocity_decay/>
        <sensor name='hokuyo_laser' type='ray'>
          <visualize>1</visualize>
          <update_rate>40</update_rate>
          <ray>
            <scan>
              <horizontal>
                <samples>720</samples>
                <resolution>1</resolution>
                <min_angle>-1.5708</min_angle>
                <max_angle>1.5708</max_angle>
              </horizontal>
              <vertical>
                <samples>1</samples>
                <min_angle>0</min_angle>
                <max_angle>0</max_angle>
              </vertical>
            </scan>
            <range>
              <min>0.1</min>
              <max>30</max>
              <resolution>0.01</resolution>
            </range>
            <noise>
              <type>gaussian</type>
              <mean>0</mean>
              <stddev>0.01</stddev>
            </noise>
          </ray>
          <plugin name='gazebo_ros_head_hokuyo_controller' filename='libgazebo_ros_laser.so'>
            <topicName>/ackermann_vehicle/scan</topicName>
            <frameName>hokuyo_laser_frame</frameName>
            <robotNamespace>/</robotNamespace>
          </plugin>
          <pose frame=''>0 0 0.11 0 -0 0</pose>
        </sensor>
        <gravity>1</gravity>
        <velocity_decay/>
        <gravity>1</gravity>
        <sensor name='imu_sensor' type='imu'>
          <always_on>1</always_on>
          <update_rate>100</update_rate>
          <visualize>1</visualize>
          <plugin name='imu_plugin' filename='libgazebo_ros_imu_sensor.so'>
            <topicName>/ackermann_vehicle/imu</topicName>
            <bodyName>imu_link</bodyName>
            <updateRateHZ>100.0</updateRateHZ>
            <gaussianNoise>0.0</gaussianNoise>
            <xyzOffset>0 0 0</xyzOffset>
            <rpyOffset>0 0 0</rpyOffset>
            <frameName>imu_link</frameName>
            <initialOrientationAsReference>0</initialOrientationAsReference>
            <robotNamespace>/</robotNamespace>
          </plugin>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <imu/>
        </sensor>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='left_steering_link'>
        <pose frame=''>0.1675 0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_front_shock' type='prismatic'>
        <child>left_steering_link</child>
        <parent>base_link</parent>
        <axis>
          <xyz>0 0 -1</xyz>
          <limit>
            <lower>-0.042</lower>
            <upper>0.042</upper>
            <effort>12.5106</effort>
            <velocity>1000</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='left_front_axle_carrier'>
        <pose frame=''>0.1675 0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_steering_joint' type='revolute'>
        <child>left_front_axle_carrier</child>
        <parent>left_steering_link</parent>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-0.785398</lower>
            <upper>0.785398</upper>
            <effort>0.5649</effort>
            <velocity>4.553</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='left_front_wheel'>
        <pose frame=''>0.1675 0.1525 -0.0235 1.5708 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.29</mass>
          <inertia>
            <ixx>0.000746466</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000746466</iyy>
            <iyz>0</iyz>
            <izz>0.00111094</izz>
          </inertia>
        </inertial>
        <collision name='left_front_wheel_collision'>
          <pose frame=''>0 -0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='left_front_wheel_visual'>
          <pose frame=''>0 -0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Black</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <gravity>1</gravity>
        <velocity_decay/>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_front_axle' type='revolute'>
        <child>left_front_wheel</child>
        <parent>left_front_axle_carrier</parent>
        <axis>
          <xyz>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='left_rear_axle_carrier'>
        <pose frame=''>-0.1675 0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_rear_shock' type='prismatic'>
        <child>left_rear_axle_carrier</child>
        <parent>base_link</parent>
        <axis>
          <xyz>0 0 -1</xyz>
          <limit>
            <lower>-0.042</lower>
            <upper>0.042</upper>
            <effort>12.5106</effort>
            <velocity>1000</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='left_rear_wheel'>
        <pose frame=''>-0.1675 0.1525 -0.0235 1.5708 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.29</mass>
          <inertia>
            <ixx>0.000746466</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000746466</iyy>
            <iyz>0</iyz>
            <izz>0.00111094</izz>
          </inertia>
        </inertial>
        <collision name='left_rear_wheel_collision'>
          <pose frame=''>0 -0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='left_rear_wheel_visual'>
          <pose frame=''>0 -0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Black</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <gravity>1</gravity>
        <velocity_decay/>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_rear_axle' type='revolute'>
        <child>left_rear_wheel</child>
        <parent>left_rear_axle_carrier</parent>
        <axis>
          <xyz>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_steering_link'>
        <pose frame=''>0.1675 -0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_front_shock' type='prismatic'>
        <child>right_steering_link</child>
        <parent>base_link</parent>
        <axis>
          <xyz>0 0 -1</xyz>
          <limit>
            <lower>-0.042</lower>
            <upper>0.042</upper>
            <effort>12.5106</effort>
            <velocity>1000</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_front_axle_carrier'>
        <pose frame=''>0.1675 -0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_steering_joint' type='revolute'>
        <child>right_front_axle_carrier</child>
        <parent>right_steering_link</parent>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-0.785398</lower>
            <upper>0.785398</upper>
            <effort>0.5649</effort>
            <velocity>4.553</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_front_wheel'>
        <pose frame=''>0.1675 -0.1525 -0.0235 1.5708 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.29</mass>
          <inertia>
            <ixx>0.000746466</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000746466</iyy>
            <iyz>0</iyz>
            <izz>0.00111094</izz>
          </inertia>
        </inertial>
        <collision name='right_front_wheel_collision'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='right_front_wheel_visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Black</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <gravity>1</gravity>
        <velocity_decay/>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_front_axle' type='revolute'>
        <child>right_front_wheel</child>
        <parent>right_front_axle_carrier</parent>
        <axis>
          <xyz>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_rear_axle_carrier'>
        <pose frame=''>-0.1675 -0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_rear_shock' type='prismatic'>
        <child>right_rear_axle_carrier</child>
        <parent>base_link</parent>
        <axis>
          <xyz>0 0 -1</xyz>
          <limit>
            <lower>-0.042</lower>
            <upper>0.042</upper>
            <effort>12.5106</effort>
            <velocity>1000</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_rear_wheel'>
        <pose frame=''>-0.1675 -0.1525 -0.0235 1.5708 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.29</mass>
          <inertia>
            <ixx>0.000746466</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000746466</iyy>
            <iyz>0</iyz>
            <izz>0.00111094</izz>
          </inertia>
        </inertial>
        <collision name='right_rear_wheel_collision'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='right_rear_wheel_visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Black</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <gravity>1</gravity>
        <velocity_decay/>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_rear_axle' type='revolute'>
        <child>right_rear_wheel</child>
        <parent>right_rear_axle_carrier</parent>
        <axis>
          <xyz>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <plugin name='gazebo_ros_control' filename='libgazebo_ros_control.so'>
        <robotNamespace>/</robotNamespace>
      </plugin>
      <static>0</static>
      <plugin name='gazebo_ros_p3d' filename='libgazebo_ros_p3d.so'>
        <alwaysOn>1</alwaysOn>
        <updateRate>30.0</updateRate>
        <bodyName>base_link</bodyName>
        <topicName>ground_truth_pose</topicName>
        <gaussianNoise>0.0</gaussianNoise>
        <frameName>map</frameName>
        <robotNamespace>/</robotNamespace>
      </plugin>
      <pose frame=''>-2 -2 1.2 0 -0 0</pose>
    </model>
    <model name='unit_box_clone_22_clone'>
      <pose frame=''>-3.46114 3.49395 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22_clone_0'>
      <pose frame=''>-3.4644 4.45117 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22_clone_1'>
      <pose frame=''>-6.48692 4.45963 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22_clone_2'>
      <pose frame=''>0.520277 2.49449 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22_clone_3'>
      <pose frame=''>-3.49026 6.51577 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
  </world>
</sdf>
