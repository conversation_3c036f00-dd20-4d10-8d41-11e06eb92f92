<sdf version='1.6'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose frame=''>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <surface>
            <contact>
              <collide_bitmask>65535</collide_bitmask>
              <ode/>
            </contact>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <gravity>0 0 -9.8</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <physics name='default_physics' default='0' type='ode'>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <audio>
      <device>default</device>
    </audio>
    <wind/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='unit_box'>
      <pose frame=''>0.590738 0.60469 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1.44302 1.6599 4.02384</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1.44302 1.6599 4.02384</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone'>
      <pose frame=''>0.5497 1.49077 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_0'>
      <pose frame=''>0.542038 3.47351 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_1'>
      <pose frame=''>0.55967 5.47509 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_2'>
      <pose frame=''>0.545616 7.45285 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_3'>
      <pose frame=''>0.543198 6.45342 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_4'>
      <pose frame=''>-2.47532 7.50096 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_5'>
      <pose frame=''>-2.47617 5.47727 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_7'>
      <pose frame=''>-2.47412 3.51977 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_8'>
      <pose frame=''>-2.50304 1.43642 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_9'>
      <pose frame=''>-2.48784 2.49434 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_10'>
      <pose frame=''>-3.45923 0.496209 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_11'>
      <pose frame=''>-6.40158 0.469192 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_12'>
      <pose frame=''>-6.48918 1.4437 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_13'>
      <pose frame=''>-6.43074 2.53456 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_14'>
      <pose frame=''>-6.48652 3.48844 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_15'>
      <pose frame=''>-6.34699 5.29292 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_16'>
      <pose frame=''>-6.34299 6.55486 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_17'>
      <pose frame=''>-6.73169 7.69456 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_19'>
      <pose frame=''>-2.42105 6.48001 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_21'>
      <pose frame=''>-3.51197 5.67389 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22'>
      <pose frame=''>-3.33477 7.37903 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='building_template_model'>
      <pose frame=''>-2.98967 4.02339 0 0 -0 0</pose>
      <link name='Wall_0'>
        <collision name='Wall_0_Collision'>
          <geometry>
            <box>
              <size>8.48924 0.15 2.5</size>
            </box>
          </geometry>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_0_Visual'>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.48924 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose frame=''>-4.55 0.007581 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_1'>
        <collision name='Wall_1_Collision'>
          <geometry>
            <box>
              <size>9.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_1_Visual'>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>9.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose frame=''>0 4.15427 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_4'>
        <collision name='Wall_4_Collision'>
          <geometry>
            <box>
              <size>8.5044 0.15 2.5</size>
            </box>
          </geometry>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_4_Visual'>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.5044 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/White</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose frame=''>4.55 -0 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <static>1</static>
    </model>
    <state world_name='default'>
      <sim_time>662 686000000</sim_time>
      <real_time>819 967769435</real_time>
      <wall_time>1593492657 600716273</wall_time>
      <iterations>470687</iterations>
      <model name='ackermann_vehicle'>
        <pose frame=''>-1.03104 -1.8347 0.105082 5.1e-05 -0.007693 1.57911</pose>
        <scale>1 1 1</scale>
        <link name='base_link'>
          <pose frame=''>-1.03104 -1.8347 0.105082 5.1e-05 -0.007693 1.57911</pose>
          <velocity>-0.000173 2.6e-05 0.000129 -0.000487 0.078 0.001524</velocity>
          <acceleration>0.465086 0.001248 0.256231 -0.327552 -0.650864 -2.75265</acceleration>
          <wrench>1.89104 0.005074 1.04183 0 -0 0</wrench>
        </link>
        <link name='left_front_axle_carrier'>
          <pose frame=''>-1.18492 -1.66822 0.073023 5.1e-05 -0.007693 1.57914</pose>
          <velocity>0.007347 0.000157 -0.003449 -0.000483 0.077704 0.002064</velocity>
          <acceleration>4.84683 0.137555 -6.69359 -0.127228 1.19907 1.10925</acceleration>
          <wrench>0.004847 0.000138 -0.006694 0 -0 0</wrench>
        </link>
        <link name='left_front_wheel'>
          <pose frame=''>-1.18493 -1.66822 0.073023 1.57085 0.005592 1.57914</pose>
          <velocity>0.005583 0.000109 -0.003448 -0.000218 0.076494 0.0021</velocity>
          <acceleration>3.69069 0.051478 -6.69166 -3.10023 0.328945 1.60359</acceleration>
          <wrench>1.0703 0.014929 -1.94058 0 -0 0</wrench>
        </link>
        <link name='left_rear_axle_carrier'>
          <pose frame=''>-1.18214 -2.00321 0.073023 5.1e-05 -0.007693 1.57911</pose>
          <velocity>0.008605 0.000232 -0.003399 -0.000478 0.077303 0.002347</velocity>
          <acceleration>5.489 0.24484 -7.06266 -0.123238 1.07198 -2.85066</acceleration>
          <wrench>0.005489 0.000245 -0.007063 0 -0 0</wrench>
        </link>
        <link name='left_rear_wheel'>
          <pose frame=''>-1.18214 -2.00321 0.073023 -1.57102 1.36726 -1.56271</pose>
          <velocity>0.005423 0.00014 -0.003398 -0.000576 0.074339 0.002206</velocity>
          <acceleration>3.6295 0.080982 -7.06019 -0.769392 -0.518208 -1.20866</acceleration>
          <wrench>1.05255 0.023485 -2.04745 0 -0 0</wrench>
        </link>
        <link name='left_steering_link'>
          <pose frame=''>-1.18493 -1.66822 0.073023 5.1e-05 -0.007693 1.57911</pose>
          <velocity>0.003137 4.3e-05 -0.003453 -0.000484 0.077862 0.001029</velocity>
          <acceleration>2.07698 -0.066156 -6.70219 -0.12814 1.30493 2.61606</acceleration>
          <wrench>0.002077 -6.6e-05 -0.006702 0 -0 0</wrench>
        </link>
        <link name='right_front_axle_carrier'>
          <pose frame=''>-0.879936 -1.66569 0.073023 5.2e-05 -0.007693 1.57907</pose>
          <velocity>0.007369 3.2e-05 -0.003449 -0.000489 0.077826 -7.6e-05</velocity>
          <acceleration>4.89069 -0.043348 -7.23118 -0.140169 1.35006 -0.370063</acceleration>
          <wrench>0.004891 -4.3e-05 -0.007231 0 -0 0</wrench>
        </link>
        <link name='right_front_wheel'>
          <pose frame=''>-0.879937 -1.66569 0.073023 -1.57085 -0.278688 -1.5625</pose>
          <velocity>0.005598 5.9e-05 -0.003447 -0.000844 0.076676 -5.5e-05</velocity>
          <acceleration>3.71687 0.043104 -7.22779 0.838894 0.6626 -0.516654</acceleration>
          <wrench>1.07789 0.0125 -2.09606 0 -0 0</wrench>
        </link>
        <link name='right_rear_axle_carrier'>
          <pose frame=''>-0.87716 -2.00068 0.073022 5.1e-05 -0.007693 1.57911</pose>
          <velocity>-0.005686 0.000208 -0.003403 -0.000492 0.078028 0.0024</velocity>
          <acceleration>-2.8461 0.571847 -6.53095 -0.143905 1.43935 -2.84772</acceleration>
          <wrench>-0.002846 0.000572 -0.006531 0 -0 0</wrench>
        </link>
        <link name='right_rear_wheel'>
          <pose frame=''>-0.877157 -2.00068 0.073023 1.57087 0.77067 1.57916</pose>
          <velocity>0.00544 5.3e-05 -0.003403 0.000579 0.07456 0.002146</velocity>
          <acceleration>3.6492 0.065107 -6.53144 -2.42844 -0.265948 -1.06491</acceleration>
          <wrench>1.05827 0.018881 -1.89412 0 -0 0</wrench>
        </link>
        <link name='right_steering_link'>
          <pose frame=''>-0.879939 -1.66569 0.073023 5.1e-05 -0.007693 1.5791</pose>
          <velocity>0.003149 9.7e-05 -0.003452 -0.00049 0.077956 0.001003</velocity>
          <acceleration>2.10084 0.160649 -7.23704 -0.140704 1.4103 2.60151</acceleration>
          <wrench>0.002101 0.000161 -0.007237 0 -0 0</wrench>
        </link>
      </model>
      <model name='building_template_model'>
        <pose frame=''>-2.98967 4.02339 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='Wall_0'>
          <pose frame=''>-7.53967 4.03097 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_1'>
          <pose frame=''>-2.98967 8.17766 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_4'>
          <pose frame=''>1.56033 4.02339 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='ground_plane'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box'>
        <pose frame=''>0.518834 0.484919 0.249258 0 -0 1e-06</pose>
        <scale>0.832461 0.776172 0.498517</scale>
        <link name='link'>
          <pose frame=''>0.518834 0.484919 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone'>
        <pose frame=''>0.5497 1.49077 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.5497 1.49077 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_0'>
        <pose frame=''>0.542038 3.47351 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.542038 3.47351 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_1'>
        <pose frame=''>0.55967 5.47509 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.55967 5.47509 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_10'>
        <pose frame=''>-3.41672 0.646729 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.41672 0.646729 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_11'>
        <pose frame=''>-6.47938 2.48543 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.47938 2.48543 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_12'>
        <pose frame=''>-6.48928 1.49952 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.48928 1.49952 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 -0 0 0 -0 0</acceleration>
          <wrench>-0 -0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_13'>
        <pose frame=''>-5.47189 7.54665 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-5.47189 7.54665 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_14'>
        <pose frame=''>-2.41544 0.611316 0.249258 0 -0 0.000133</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.41544 0.611316 0.249258 0 -0 0.000133</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_15'>
        <pose frame=''>-6.5582 4.51505 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.5582 4.51505 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_16'>
        <pose frame=''>-6.50451 0.518649 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.50451 0.518649 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_17'>
        <pose frame=''>-0.470282 7.45877 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-0.470282 7.45877 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_19'>
        <pose frame=''>-2.46049 5.42737 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.46049 5.42737 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_2'>
        <pose frame=''>-2.41813 7.52932 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.41813 7.52932 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_21'>
        <pose frame=''>-3.45665 5.46799 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.45665 5.46799 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -0 0 -0 0</acceleration>
          <wrench>0 0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22'>
        <pose frame=''>-3.41126 1.51793 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.41126 1.51793 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone'>
        <pose frame=''>-3.51782 7.53227 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.51782 7.53227 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone_0'>
        <pose frame=''>-3.41838 3.54905 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.41838 3.54905 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone_1'>
        <pose frame=''>-6.55843 6.52202 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.55843 6.52202 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone_2'>
        <pose frame=''>0.520277 2.49449 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.520277 2.49449 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22_clone_3'>
        <pose frame=''>-4.50332 7.52217 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-4.50332 7.52217 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_3'>
        <pose frame=''>0.543198 6.45342 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.543198 6.45342 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_4'>
        <pose frame=''>-2.45822 2.49616 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.45822 2.49616 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_5'>
        <pose frame=''>-1.40949 7.51079 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-1.40949 7.51079 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_7'>
        <pose frame=''>-2.45609 4.50801 0.249258 0 -0 1.7e-05</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.45609 4.50801 0.249258 0 -0 1.7e-05</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -0 0 -0 0</acceleration>
          <wrench>0 0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_8'>
        <pose frame=''>-2.39586 1.50119 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.39586 1.50119 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_9'>
        <pose frame=''>-3.39692 4.53196 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.39692 4.53196 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <light name='sun'>
        <pose frame=''>0 0 10 0 -0 0</pose>
      </light>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose frame=''>-3.00136 4.13919 18.6662 0 1.5698 -3.13818</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
    <model name='ackermann_vehicle'>
      <link name='base_link'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <inertial>
          <pose frame=''>-0.006192 0 0.004005 0 -0 0</pose>
          <mass>4.066</mass>
          <inertia>
            <ixx>0.0157528</ixx>
            <ixy>0</ixy>
            <ixz>-8.77514e-05</ixz>
            <iyy>0.023805</iyy>
            <iyz>0</iyz>
            <izz>0.0303749</izz>
          </inertia>
        </inertial>
        <collision name='base_link_fixed_joint_lump__chassis_collision'>
          <pose frame=''>0 0 -0.005 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.258 0.168 0.01</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <collision name='base_link_fixed_joint_lump__left_battery_collision_1'>
          <pose frame=''>-0.025 0.057319 0.00052 0.349066 -0 0</pose>
          <geometry>
            <box>
              <size>0.16 0.047 0.024</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <collision name='base_link_fixed_joint_lump__right_battery_collision_2'>
          <pose frame=''>-0.025 -0.057319 0.00052 -0.349066 0 0</pose>
          <geometry>
            <box>
              <size>0.16 0.047 0.024</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <collision name='base_link_fixed_joint_lump__hokuyo_laser_frame_collision_3'>
          <pose frame=''>0 0 0.0985 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.058 0.058 0.087</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='base_link_visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.01 0.01 0.01</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__chassis_visual_1'>
          <pose frame=''>0 0 -0.005 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.258 0.168 0.01</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__left_battery_visual_2'>
          <pose frame=''>-0.025 0.057319 0.00052 0.349066 -0 0</pose>
          <geometry>
            <box>
              <size>0.16 0.047 0.024</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Blue</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__right_battery_visual_3'>
          <pose frame=''>-0.025 -0.057319 0.00052 -0.349066 0 0</pose>
          <geometry>
            <box>
              <size>0.16 0.047 0.024</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Blue</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__hokuyo_laser_frame_visual_4'>
          <pose frame=''>0 0 0.11 0 -0 0</pose>
          <geometry>
            <mesh>
              <scale>1 1 1</scale>
              <uri>/home/<USER>/catkin_ws/src/hector_models/hector_sensors_description/meshes/hokuyo_utm30lx/hokuyo_utm_30lx.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>__default__</uri>
              <name>__default__</name>
            </script>
          </material>
        </visual>
        <visual name='base_link_fixed_joint_lump__imu_link_visual_5'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.02 0.035 0.002</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>__default__</uri>
              <name>__default__</name>
            </script>
          </material>
        </visual>
        <velocity_decay/>
        <velocity_decay/>
        <velocity_decay/>
        <velocity_decay/>
        <velocity_decay/>
        <sensor name='hokuyo_laser' type='ray'>
          <visualize>1</visualize>
          <update_rate>40</update_rate>
          <ray>
            <scan>
              <horizontal>
                <samples>720</samples>
                <resolution>1</resolution>
                <min_angle>-1.5708</min_angle>
                <max_angle>1.5708</max_angle>
              </horizontal>
              <vertical>
                <samples>1</samples>
                <min_angle>0</min_angle>
                <max_angle>0</max_angle>
              </vertical>
            </scan>
            <range>
              <min>0.1</min>
              <max>30</max>
              <resolution>0.01</resolution>
            </range>
            <noise>
              <type>gaussian</type>
              <mean>0</mean>
              <stddev>0.01</stddev>
            </noise>
          </ray>
          <plugin name='gazebo_ros_head_hokuyo_controller' filename='libgazebo_ros_laser.so'>
            <topicName>/ackermann_vehicle/scan</topicName>
            <frameName>hokuyo_laser_frame</frameName>
            <robotNamespace>/</robotNamespace>
          </plugin>
          <pose frame=''>0 0 0.11 0 -0 0</pose>
        </sensor>
        <gravity>1</gravity>
        <velocity_decay/>
        <gravity>1</gravity>
        <sensor name='imu_sensor' type='imu'>
          <always_on>1</always_on>
          <update_rate>100</update_rate>
          <visualize>1</visualize>
          <plugin name='imu_plugin' filename='libgazebo_ros_imu_sensor.so'>
            <topicName>/ackermann_vehicle/imu</topicName>
            <bodyName>imu_link</bodyName>
            <updateRateHZ>100.0</updateRateHZ>
            <gaussianNoise>0.0</gaussianNoise>
            <xyzOffset>0 0 0</xyzOffset>
            <rpyOffset>0 0 0</rpyOffset>
            <frameName>imu_link</frameName>
            <initialOrientationAsReference>0</initialOrientationAsReference>
            <robotNamespace>/</robotNamespace>
          </plugin>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <imu/>
        </sensor>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='left_steering_link'>
        <pose frame=''>0.1675 0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_front_shock' type='prismatic'>
        <child>left_steering_link</child>
        <parent>base_link</parent>
        <axis>
          <xyz>0 0 -1</xyz>
          <limit>
            <lower>-0.042</lower>
            <upper>0.042</upper>
            <effort>12.5106</effort>
            <velocity>1000</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='left_front_axle_carrier'>
        <pose frame=''>0.1675 0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_steering_joint' type='revolute'>
        <child>left_front_axle_carrier</child>
        <parent>left_steering_link</parent>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-0.785398</lower>
            <upper>0.785398</upper>
            <effort>0.5649</effort>
            <velocity>4.553</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='left_front_wheel'>
        <pose frame=''>0.1675 0.1525 -0.0235 1.5708 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.29</mass>
          <inertia>
            <ixx>0.000746466</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000746466</iyy>
            <iyz>0</iyz>
            <izz>0.00111094</izz>
          </inertia>
        </inertial>
        <collision name='left_front_wheel_collision'>
          <pose frame=''>0 -0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='left_front_wheel_visual'>
          <pose frame=''>0 -0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Black</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <gravity>1</gravity>
        <velocity_decay/>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_front_axle' type='revolute'>
        <child>left_front_wheel</child>
        <parent>left_front_axle_carrier</parent>
        <axis>
          <xyz>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='left_rear_axle_carrier'>
        <pose frame=''>-0.1675 0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_rear_shock' type='prismatic'>
        <child>left_rear_axle_carrier</child>
        <parent>base_link</parent>
        <axis>
          <xyz>0 0 -1</xyz>
          <limit>
            <lower>-0.042</lower>
            <upper>0.042</upper>
            <effort>12.5106</effort>
            <velocity>1000</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='left_rear_wheel'>
        <pose frame=''>-0.1675 0.1525 -0.0235 1.5708 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.29</mass>
          <inertia>
            <ixx>0.000746466</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000746466</iyy>
            <iyz>0</iyz>
            <izz>0.00111094</izz>
          </inertia>
        </inertial>
        <collision name='left_rear_wheel_collision'>
          <pose frame=''>0 -0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='left_rear_wheel_visual'>
          <pose frame=''>0 -0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Black</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <gravity>1</gravity>
        <velocity_decay/>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='left_rear_axle' type='revolute'>
        <child>left_rear_wheel</child>
        <parent>left_rear_axle_carrier</parent>
        <axis>
          <xyz>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_steering_link'>
        <pose frame=''>0.1675 -0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_front_shock' type='prismatic'>
        <child>right_steering_link</child>
        <parent>base_link</parent>
        <axis>
          <xyz>0 0 -1</xyz>
          <limit>
            <lower>-0.042</lower>
            <upper>0.042</upper>
            <effort>12.5106</effort>
            <velocity>1000</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_front_axle_carrier'>
        <pose frame=''>0.1675 -0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_steering_joint' type='revolute'>
        <child>right_front_axle_carrier</child>
        <parent>right_steering_link</parent>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-0.785398</lower>
            <upper>0.785398</upper>
            <effort>0.5649</effort>
            <velocity>4.553</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_front_wheel'>
        <pose frame=''>0.1675 -0.1525 -0.0235 1.5708 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.29</mass>
          <inertia>
            <ixx>0.000746466</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000746466</iyy>
            <iyz>0</iyz>
            <izz>0.00111094</izz>
          </inertia>
        </inertial>
        <collision name='right_front_wheel_collision'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='right_front_wheel_visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Black</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <gravity>1</gravity>
        <velocity_decay/>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_front_axle' type='revolute'>
        <child>right_front_wheel</child>
        <parent>right_front_axle_carrier</parent>
        <axis>
          <xyz>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_rear_axle_carrier'>
        <pose frame=''>-0.1675 -0.1525 -0.0235 0 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.001</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.001</iyy>
            <iyz>0</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_rear_shock' type='prismatic'>
        <child>right_rear_axle_carrier</child>
        <parent>base_link</parent>
        <axis>
          <xyz>0 0 -1</xyz>
          <limit>
            <lower>-0.042</lower>
            <upper>0.042</upper>
            <effort>12.5106</effort>
            <velocity>1000</velocity>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <link name='right_rear_wheel'>
        <pose frame=''>-0.1675 -0.1525 -0.0235 1.5708 -0 0</pose>
        <inertial>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <mass>0.29</mass>
          <inertia>
            <ixx>0.000746466</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.000746466</iyy>
            <iyz>0</iyz>
            <izz>0.00111094</izz>
          </inertia>
        </inertial>
        <collision name='right_rear_wheel_collision'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='right_rear_wheel_visual'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <geometry>
            <cylinder>
              <length>0.0889</length>
              <radius>0.073025</radius>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Black</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <gravity>1</gravity>
        <velocity_decay/>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='right_rear_axle' type='revolute'>
        <child>right_rear_wheel</child>
        <parent>right_rear_axle_carrier</parent>
        <axis>
          <xyz>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
          <use_parent_model_frame>1</use_parent_model_frame>
        </axis>
      </joint>
      <plugin name='gazebo_ros_control' filename='libgazebo_ros_control.so'>
        <robotNamespace>/</robotNamespace>
      </plugin>
      <static>0</static>
      <plugin name='gazebo_ros_p3d' filename='libgazebo_ros_p3d.so'>
        <alwaysOn>1</alwaysOn>
        <updateRate>30.0</updateRate>
        <bodyName>base_link</bodyName>
        <topicName>ground_truth_pose</topicName>
        <gaussianNoise>0.0</gaussianNoise>
        <frameName>map</frameName>
        <robotNamespace>/</robotNamespace>
      </plugin>
      <pose frame=''>-2 -2 1.2 0 -0 0</pose>
    </model>
    <model name='unit_box_clone_22_clone'>
      <pose frame=''>-3.46114 3.49395 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22_clone_0'>
      <pose frame=''>-3.4644 4.45117 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22_clone_1'>
      <pose frame=''>-6.48692 4.45963 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22_clone_2'>
      <pose frame=''>0.520277 2.49449 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22_clone_3'>
      <pose frame=''>-3.49026 6.51577 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
  </world>
</sdf>
