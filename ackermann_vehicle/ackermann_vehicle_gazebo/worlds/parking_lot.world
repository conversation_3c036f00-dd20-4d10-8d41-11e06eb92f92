<sdf version='1.6'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose frame=''>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <surface>
            <contact>
              <collide_bitmask>65535</collide_bitmask>
              <ode/>
            </contact>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <gravity>0 0 -9.8</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <physics name='default_physics' default='0' type='ode'>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <audio>
      <device>default</device>
    </audio>
    <wind/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='unit_box'>
      <pose frame=''>0.590738 0.60469 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone'>
      <pose frame=''>0.5497 1.49077 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_0'>
      <pose frame=''>0.542038 3.47351 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_1'>
      <pose frame=''>0.55967 5.47509 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_2'>
      <pose frame=''>0.545616 7.45285 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_3'>
      <pose frame=''>0.543198 6.45342 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_4'>
      <pose frame=''>-2.47532 7.50096 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_5'>
      <pose frame=''>-2.47617 5.47727 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_6'>
      <pose frame=''>-2.4761 4.43201 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_7'>
      <pose frame=''>-2.47412 3.51977 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_8'>
      <pose frame=''>-2.50304 1.43642 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_9'>
      <pose frame=''>-2.48784 2.49434 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_10'>
      <pose frame=''>-3.45923 0.496209 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_11'>
      <pose frame=''>-6.40158 0.469192 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_12'>
      <pose frame=''>-6.48918 1.4437 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_13'>
      <pose frame=''>-6.43074 2.53456 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_14'>
      <pose frame=''>-6.48652 3.48844 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_15'>
      <pose frame=''>-6.34699 5.29292 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_16'>
      <pose frame=''>-6.34299 6.55486 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_17'>
      <pose frame=''>-6.73169 7.69456 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_19'>
      <pose frame=''>-2.42105 6.48001 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_20'>
      <pose frame=''>-3.53767 3.50066 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_21'>
      <pose frame=''>-3.51197 5.67389 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_clone_22'>
      <pose frame=''>-3.33477 7.37903 0.249259 0 -0 1e-06</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.832462 0.776172 0.498517</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='building_template_model'>
      <pose frame=''>-2.98967 4.02339 0 0 -0 0</pose>
      <link name='Wall_0'>
        <collision name='Wall_0_Collision'>
          <geometry>
            <box>
              <size>8.48924 0.15 2.5</size>
            </box>
          </geometry>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_0_Visual'>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.48924 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose frame=''>-4.55 0.007581 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_1'>
        <collision name='Wall_1_Collision'>
          <geometry>
            <box>
              <size>9.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_1_Visual'>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>9.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose frame=''>0 4.15427 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_4'>
        <collision name='Wall_4_Collision'>
          <geometry>
            <box>
              <size>8.5044 0.15 2.5</size>
            </box>
          </geometry>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_4_Visual'>
          <pose frame=''>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.5044 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose frame=''>4.55 -0 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <static>1</static>
    </model>
    <state world_name='default'>
      <sim_time>191 999000000</sim_time>
      <real_time>193 532879487</real_time>
      <wall_time>1590733145 580670622</wall_time>
      <iterations>191999</iterations>
      <model name='building_template_model'>
        <pose frame=''>-2.98967 4.02339 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='Wall_0'>
          <pose frame=''>-7.53967 4.03097 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_1'>
          <pose frame=''>-2.98967 8.17766 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_4'>
          <pose frame=''>1.56033 4.02339 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='ground_plane'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box'>
        <pose frame=''>0.518834 0.484919 0.249258 0 -0 1e-06</pose>
        <scale>0.832461 0.776172 0.498517</scale>
        <link name='link'>
          <pose frame=''>0.518834 0.484919 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 0 0 -0 0</acceleration>
          <wrench>0 -0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone'>
        <pose frame=''>0.5497 1.49077 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.5497 1.49077 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_0'>
        <pose frame=''>0.542038 3.47351 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.542038 3.47351 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_1'>
        <pose frame=''>0.55967 5.47509 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.55967 5.47509 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_10'>
        <pose frame=''>-3.45923 0.496209 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.45923 0.496209 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_11'>
        <pose frame=''>-6.40158 0.469192 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.40158 0.469192 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_12'>
        <pose frame=''>-6.48918 1.4437 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.48918 1.4437 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_13'>
        <pose frame=''>-6.43074 2.53456 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.43074 2.53456 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_14'>
        <pose frame=''>-6.48652 3.48844 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.48652 3.48844 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_15'>
        <pose frame=''>-6.45405 5.48233 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.45405 5.48233 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_16'>
        <pose frame=''>-6.50367 6.55587 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.50367 6.55587 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_17'>
        <pose frame=''>-6.48562 7.5467 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-6.48562 7.5467 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_19'>
        <pose frame=''>-2.42105 6.48001 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.42105 6.48001 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_2'>
        <pose frame=''>0.545616 7.45285 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.545616 7.45285 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_20'>
        <pose frame=''>-3.53767 3.50066 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.53767 3.50066 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_21'>
        <pose frame=''>-3.4752 5.44171 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.4752 5.44171 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_22'>
        <pose frame=''>-3.51755 7.5323 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-3.51755 7.5323 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_3'>
        <pose frame=''>0.543198 6.45342 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0.543198 6.45342 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_4'>
        <pose frame=''>-2.47532 7.50096 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.47532 7.50096 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_5'>
        <pose frame=''>-2.52887 5.46662 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.52887 5.46662 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_6'>
        <pose frame=''>-2.4761 4.43201 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.4761 4.43201 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_7'>
        <pose frame=''>-2.47412 3.51977 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.47412 3.51977 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_8'>
        <pose frame=''>-2.50304 1.43642 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.50304 1.43642 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 0 0 0 -0 0</acceleration>
          <wrench>-0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_clone_9'>
        <pose frame=''>-2.48784 2.49434 0.249258 0 -0 1e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>-2.48784 2.49434 0.249258 0 -0 1e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 -0 -0 0 -0 0</acceleration>
          <wrench>0 -0 -0 0 -0 0</wrench>
        </link>
      </model>
      <light name='sun'>
        <pose frame=''>0 0 10 0 -0 0</pose>
      </light>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose frame=''>0.421824 -7.97338 13.8764 -0 0.791643 1.79301</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
  </world>
</sdf>
