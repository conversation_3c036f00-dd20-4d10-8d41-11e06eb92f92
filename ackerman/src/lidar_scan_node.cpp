#include "ros/ros.h"
#include "sensor_msgs/LaserScan.h"
#include "std_msgs/Float32MultiArray.h"
#include "geometry_msgs/Point.h"
#include <cmath>
#include <iostream>
#include <vector>
#include <utility>

ros::Publisher pub_all_distances;
ros::Publisher pub_all_angles;
ros::Publisher pub_distances_90_to_180;
ros::Publisher pub_distances_minus180_to_minus90;
ros::Publisher pub_distances_minus150_to_150;

void laserCallback(const sensor_msgs::LaserScan::ConstPtr& msg) {
    std::vector<float> all_distances;
    std::vector<float> all_angles;
    std::vector<std::pair<float, float>> distances_90_to_180;
    std::vector<std::pair<float, float>> distances_minus180_to_minus90;
    std::vector<std::pair<float, float>> distances_minus150_to_150;

    distances_90_to_180.clear();
    distances_minus180_to_minus90.clear();
    distances_minus150_to_150.clear();
    all_distances.clear();
    all_angles.clear();

    const std::vector<float>& ranges = msg->ranges;
    float angle_increment = msg->angle_increment;
    float angle_min = msg->angle_min;

    for (size_t i = 0; i < ranges.size(); ++i) {
        float distance = ranges[i];
        if (distance > 5.0) continue;

        float angle = angle_min + i * angle_increment;
        float angle_degrees = angle * 180.0 / M_PI; // Convert to degrees

        // if (angle_degrees <= 0) {
        //     angle_degrees += 180;
        // } 
        // else if (angle_degrees >= 0) {
        //     angle_degrees -= 180;
        // }

        all_distances.push_back(distance);
        all_angles.push_back(angle_degrees);

        if (angle_degrees >= 90 && angle_degrees <= 150) {
            distances_90_to_180.emplace_back(distance, angle_degrees);
        } 
        else if (angle_degrees >= -150 && angle_degrees <= -90) {
            distances_minus180_to_minus90.emplace_back(distance, angle_degrees);
        } 
        if (angle_degrees <= -170 && angle_degrees >= -180 || angle_degrees <= 180 && angle_degrees >= 170) {
            distances_minus150_to_150.emplace_back(distance, angle_degrees);
        }
    }

    std_msgs::Float32MultiArray all_distances_msg;
    all_distances_msg.data = all_distances;
    pub_all_distances.publish(all_distances_msg);

    std_msgs::Float32MultiArray all_angles_msg;
    all_angles_msg.data = all_angles;
    pub_all_angles.publish(all_angles_msg);

    std_msgs::Float32MultiArray distances_90_to_180_msg;
    for (const auto& pair : distances_90_to_180) {
        distances_90_to_180_msg.data.push_back(pair.first);
        distances_90_to_180_msg.data.push_back(pair.second);
    }
    pub_distances_90_to_180.publish(distances_90_to_180_msg);

    std_msgs::Float32MultiArray distances_minus180_to_minus90_msg;
    for (const auto& pair : distances_minus180_to_minus90) {
        distances_minus180_to_minus90_msg.data.push_back(pair.first);
        distances_minus180_to_minus90_msg.data.push_back(pair.second);
    }
    pub_distances_minus180_to_minus90.publish(distances_minus180_to_minus90_msg);

    // for (const auto& distance : distances_minus150_to_150) {
    //     std::cout << "Distance: " << distance.first
    //               << ", Angle: " << distance.second << '\n';
    // }

    std_msgs::Float32MultiArray distances_minus150_to_150_msg;
    for (const auto& pair : distances_minus150_to_150) {
        distances_minus150_to_150_msg.data.push_back(pair.first);
        distances_minus150_to_150_msg.data.push_back(pair.second);
    }
    pub_distances_minus150_to_150.publish(distances_minus150_to_150_msg);
}

int main(int argc, char **argv) {
    ros::init(argc, argv, "lidar_scan_node");
    ros::NodeHandle n;

    pub_all_distances = n.advertise<std_msgs::Float32MultiArray>("/all_distances", 1000);
    pub_all_angles = n.advertise<std_msgs::Float32MultiArray>("/all_angles", 1000);
    pub_distances_90_to_180 = n.advertise<std_msgs::Float32MultiArray>("/distances_90_to_180", 1000);
    pub_distances_minus180_to_minus90 = n.advertise<std_msgs::Float32MultiArray>("/distances_minus180_to_minus90", 1000);
    pub_distances_minus150_to_150 = n.advertise<std_msgs::Float32MultiArray>("/distances_minus150_to_150", 1000); // New publisher

    ros::Subscriber sub = n.subscribe("/scan", 1000, laserCallback);
    ros::spin();

    return 0;
}
