#include <stdio.h>
#include <math.h>
#include <ros/ros.h>
#include <std_msgs/Float32MultiArray.h>
#include <std_msgs/Float64.h>
#include <std_msgs/Float32.h>

/*IF YOU WANT TO TEST THE FUZZY JUST UNCOMMENT THE DEFINE BELOW DAWG*/
// #define TESTING

float speed = 0;  
float distance = 0;

/*==== Membership function Speed Set START ====*/
float speed_membership_low(float x) {
    if (x < 0.0 || x > 1.5) return 0.0;
    return (1.5 - x) / 1.5;
}

float speed_membership_medium(float x) {
    if (x < 0.3 || x > 2.7) return 0.0;
    if (x <= 1.5) return (x - 0.3) / (1.5 - 0.3);
    else return (2.7 - x) / (2.7 - 1.5);          
}

float speed_membership_high(float x) {
    if (x < 1.5 || x > 3.0) return 0.0;
    return (x - 1.5) / 1.5;
}
/*==== Membership function Speed Set END ====*/

/*==== Membership function Distance START ====*/
float distance_membership_close(float x) {
    if (x < 0.0 || x > 1.5) return 0.0;
    return (1.5 - x) / 1.5;
}

float distance_membership_medium(float x) {
    if (x < 0.3 || x > 2.7) return 0.0;
    if (x <= 1.5) return (x - 0.3) / (1.5 - 0.3);
    else return (2.7 - x) / (2.7 - 1.5); 
}

float distance_membership_far(float x) {
    if (x < 1.5 || x > 3.0) return 0.0;
    return (x - 1.5) / 1.5;
}
/*==== Membership function Distance END ====*/

/*==== Membership function output (-1 to 1) START ====*/
float acceleration_membership_negative(float x) {
    if (x < 0 || x > 0.5) return 0;
    return (0.5 - x) / 0.5;
}

float acceleration_membership_zero(float x) {
    if (x < 0.2 || x > 0.8) return 0;
    if (x <= 0.5)
        return (x - 0.2) / 0.3;
    else
        return (0.8 - x) / 0.3;
}

float acceleration_membership_positive(float x) {
    if (x < 0.5 || x > 1) return 0;
    return (x - 0.5) / 0.5;
}
/*==== Membership function output END ====*/

// Centroid defuzzification
float defuzzify(float neg_strength, float zero_strength, float pos_strength) {
    float numerator = 0.0;
    float denominator = 0.0;
    const float STEP = 0.01;
    const float OUTPUT_MIN = 0.0;
    const float OUTPUT_MAX = 1.0;

    for (float x = OUTPUT_MIN; x <= OUTPUT_MAX; x += STEP) {
        float neg_mf = acceleration_membership_negative(x) * neg_strength;
        float zero_mf = acceleration_membership_zero(x) * zero_strength;
        float pos_mf = acceleration_membership_positive(x) * pos_strength;

        float total = neg_mf + zero_mf + pos_mf;
        numerator += x * total;
        denominator += total;
    }

    return (denominator == 0.0) ? 0.0 : numerator / denominator;
}

// Fuzzy Inference System
float fuzzy_controller(float speed, float distance) {
    // Fuzzify inputs
    float low_speed = speed_membership_low(speed);
    float medium_speed = speed_membership_medium(speed);
    float high_speed = speed_membership_high(speed);

    float close_distance = distance_membership_close(distance);
    float medium_distance = distance_membership_medium(distance);
    float far_distance = distance_membership_far(distance);

    float acc_neg = 0.0;   // Decelerate
    float acc_zero = 0.0;  // Constant
    float acc_pos = 0.0;   // Accelerate

    // Rule 1: Close & Fast → Decelerate
    float r1 = fmin(close_distance, high_speed);
    acc_pos = fmax(acc_pos, r1);

    // Rule 2: Mid & Mid → Constant
    float r2 = fmin(medium_distance, medium_speed);
    acc_zero = fmax(acc_zero, r2);

    // Rule 3: Far & Medium → Accelerate
    float r3 = fmin(far_distance, low_speed);
    acc_neg = fmax(acc_neg, r3);

    // Rule 4: Close & Mid → Decelerate
    float r4 = fmin(close_distance, medium_speed);
    acc_pos = fmax(acc_pos, r4);

    // Rule 5: Close & Slow → Constant
    float r5 = fmin(close_distance, low_speed);
    acc_pos = fmax(acc_pos, r5);

    // Rule 6: Mid & Fast → Decelerate
    float r6 = fmin(medium_distance, high_speed);
    acc_pos = fmax(acc_pos, r6);

    // Rule 7: Mid & Mid → Constant
    float r7 = fmin(medium_distance, medium_speed);
    acc_zero = fmax(acc_zero, r7);

    // Rule 8: Far & Slow → Accelerate
    float r8 = fmin(far_distance, medium_speed);
    acc_neg = fmax(acc_neg, r8);

    // Rule 9: Far & Fast → Constant
    float r9 = fmin(far_distance, high_speed);
    acc_neg = fmax(acc_neg, r9);

    return defuzzify(acc_neg, acc_zero, acc_pos);
}

#ifdef TESTING
void controlCallback(const std_msgs::Float32MultiArray::ConstPtr& array_msg) {
    float up_down_value = array_msg->data[4];
    float left_right_value = array_msg->data[5];

    if(up_down_value != 0){
        if(left_right_value == 1){
            distance_to_car = distance_to_car + (up_down_value);
        }
        else if(left_right_value == 2){
            speed_setpoint = speed_setpoint + (up_down_value);
        }
        else if(left_right_value == 3){
            //Do nothing
        }
        float acceleration = fuzzy_controller(speed, distance);

        printf("Distance: %.2f \n", distance_to_car);
        printf("Speed set: %.2f \n", speed_setpoint);
        printf("Acceleration/Deceleration: %.2f \n", acceleration);
    }    
    
}
#endif

void distanceCallback(const std_msgs::Float32::ConstPtr& msg) {
    distance = msg->data;
}

void speed_encoder_callback(const std_msgs::Float64::ConstPtr& msg) {
    speed = static_cast<float>(msg->data);
    if(speed < 0){
        speed = speed * -1;
    }
}

int main(int argc, char** argv) {
    ros::init(argc, argv, "fuzzy_logic");
    ros::NodeHandle nh;

    ros::Publisher pub = nh.advertise<std_msgs::Float32>("/fuzzy", 10);

    #ifdef TESTING
    ros::Subscriber sub = nh.subscribe("/control_array", 10, controlCallback);
    #endif

    ros::Subscriber sub_distance = nh.subscribe("/near_distance", 10, distanceCallback);
    ros::Subscriber speed_encoder_sub = nh.subscribe("/vehicle_speed", 10, speed_encoder_callback);

    ros::Rate rate(30);
    while (ros::ok()) {
        float acceleration = fuzzy_controller(speed, distance);

        printf("Distance: %.2f \n", distance);
        printf("Speed set: %.2f \n", speed);
        printf("Acceleration/Deceleration: %.2f \n", acceleration);

        std_msgs::Float32 gain;
        gain.data = acceleration; 
        // gain.data = 1; 
        pub.publish(gain);

        ros::spinOnce(); 
        rate.sleep();
    }

    return 0;
}
