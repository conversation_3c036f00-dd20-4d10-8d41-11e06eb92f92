#include "ros/ros.h"
#include "std_msgs/Float32MultiArray.h"
#include "geometry_msgs/Point.h"
#include "std_msgs/Float32.h"
#include <iostream>
#include <vector>
#include <cmath>
#include <numeric>
#include <algorithm>
#include <utility>
#include <chrono>
#include <ctime>
#include <fstream>
#include <cstring> // For strerror
#include <sstream>

ros::Publisher magnitude_publisher;
ros::Publisher direction_publisher;
ros::Publisher vector_y_publisher;
ros::Publisher vector_x_publisher;

std::ofstream log_file;
std::string filename; // Declare globally or pass to functions
std::vector<std::pair<float, float>> distances_90_to_180;
std::vector<std::pair<float, float>> distances_minus180_to_minus90;
std::vector<std::pair<float, float>> distances_minus150_to_150;

float v0 = 0.8;
float m = 1;
float dt = 0.001;
float resultant_magnitude = 0.0;
float resultant_angle = 0.0;

void distances90to180Callback(const std_msgs::Float32MultiArray::ConstPtr& msg) {
    distances_90_to_180.clear();
    
    if (msg->data.size() % 2 != 0) {
        ROS_WARN("Received data size is not even. Skipping update.");
        return;
    }

    for (size_t i = 0; i < msg->data.size(); i += 2) {
        float distance = msg->data[i];
        float angle = msg->data[i + 1];
        distances_90_to_180.emplace_back(distance, angle);
    }
}

void distancesMinus150to150Callback(const std_msgs::Float32MultiArray::ConstPtr& msg) {
    distances_minus150_to_150.clear();

    if (msg->data.size() % 2 != 0) {
        ROS_WARN("Received data size for distances_minus150_to_150 is not even. Skipping update.");
        return;
    }

    for (size_t i = 0; i < msg->data.size(); i += 2) {
        float distance = msg->data[i];
        float angle = msg->data[i + 1];
        distances_minus150_to_150.emplace_back(distance, angle);
    }
}

void distancesMinus180toMinus90Callback(const std_msgs::Float32MultiArray::ConstPtr& msg) {
    distances_minus180_to_minus90.clear();

    if (msg->data.size() % 2 != 0) {
        ROS_WARN("Received data size for distances_minus180_to_minus90 is not even. Skipping update.");
        return;
    }

    for (size_t i = 0; i < msg->data.size(); i += 2) {
        float distance = msg->data[i];
        float angle = msg->data[i + 1];
        distances_minus180_to_minus90.emplace_back(distance, angle);
    }
}

float calculateCoordinates(float degree) {
    float a = 1; 
    float b = 0.5;  

    float angle = degree * M_PI / 180.0;

    float r = (a * b) / std::sqrt(std::pow(b * std::cos(angle), 2) + std::pow(a * std::sin(angle), 2));

    float x = r * std::cos(angle);
    float y = r * std::sin(angle);

    float distance = std::sqrt(x * x + y * y);
    
    return distance;
}

float forceSocial(float distance, float degree) {
    float k = 1;
    float psi = 0.20;

    float r_value = calculateCoordinates(degree);
    float force = k * std::exp((r_value - distance) / psi);

    return force;
}

float forcePhysical(float distance, float degree) {
    float k = 1;  

    float r_value = calculateCoordinates(degree);
    float force = k * (r_value - distance);

    return force;
}

std::string getROSTimestamp() {
    // Get the current ROS time
    ros::Time now = ros::Time::now();

    // Convert ROS time to seconds
    std::time_t seconds = now.sec;

    // Convert to standard time structure
    std::tm* time_info = std::localtime(&seconds);

    // Format the time string
    std::ostringstream timestamp;
    timestamp << std::put_time(time_info, "%Y-%m-%d %H:%M:%S"); // Format: YYYY-MM-DD HH:MM:SS

    // Add nanoseconds for full ROS time precision
    timestamp << "." << std::setw(9) << std::setfill('0') << now.nsec;

    return timestamp.str();
}

void sfm() {
    // Open log file in append mode
    log_file.open(filename, std::ios::out | std::ios::app);
    if (!log_file.is_open()) {
        ROS_ERROR("Failed to open log file");
        ROS_INFO("FAILED OPENNING: %s", filename.c_str());
        return;
    }
    
    auto nearest_90_to_180 = std::min_element(distances_90_to_180.begin(), distances_90_to_180.end(),
                                                [](const std::pair<float, float>& a, const std::pair<float, float>& b) {
                                                    return a.first < b.first;
                                                });

    auto nearest_minus180_to_minus90 = std::min_element(distances_minus180_to_minus90.begin(), distances_minus180_to_minus90.end(),
                                                            [](const std::pair<float, float>& a, const std::pair<float, float>& b) {
                                                                return a.first < b.first;
                                                            });

    // auto nearest_minus150_to_150 = std::min_element(distances_minus150_to_150.begin(), distances_minus150_to_150.end(),
    //                                                     [](const std::pair<float, float>& a, const std::pair<float, float>& b) {
    //                                                         return a.first < b.first;
    //                                                     });                           

    std::vector<float> distances = {2.0}; 
    std::vector<float> angles = {180.0}; 

    // std::time_t now = std::time(nullptr);
    // std::string timestamp = std::ctime(&now);
    // timestamp.pop_back(); // Remove trailing newline

    std::string timestamp = getROSTimestamp();

    log_file << timestamp << ",";
    log_file << resultant_magnitude << ",";
    log_file << resultant_angle << ",";

    // if (!distances_minus150_to_150.empty()) {
    //     float nearest_distance_minus150_to_150 = nearest_minus150_to_150->first;
    //     float nearest_angle_minus150_to_150 = nearest_minus150_to_150->second;
    //     log_file << nearest_distance_minus150_to_150 << ",";
    //     log_file << nearest_angle_minus150_to_150 << ",";

    //     float x_minus150_to_150 = nearest_distance_minus150_to_150 * cos(nearest_angle_minus150_to_150 * M_PI / 180.0);
    //     float y_minus150_to_150 = nearest_distance_minus150_to_150 * sin(nearest_angle_minus150_to_150 * M_PI / 180.0);

    //     float f_social = forceSocial(nearest_distance_minus150_to_150, nearest_angle_minus150_to_150);
    //     float f_physical = forcePhysical(nearest_distance_minus150_to_150, nearest_angle_minus150_to_150);
    //     float f_force_static = f_social + f_physical;
    //     float f_angle_reverse = nearest_angle_minus150_to_150 * -1;

    //     //std::cout << "Obstacle depan (degree): " << nearest_angle_minus150_to_150 << ", Distance: " << nearest_distance_minus150_to_150 << " m\n";

    //     distances.push_back(f_force_static);
    //     angles.push_back(f_angle_reverse);
    // }
    // else{
    //     log_file << "No Data"<< ",";
    //     log_file << "No Data"<< ",";
    // }


    if (!distances_90_to_180.empty()) {
        float nearest_distance_90_to_180 = nearest_90_to_180->first;
        float nearest_angle_90_to_180 = nearest_90_to_180->second;
        log_file << nearest_distance_90_to_180 << ",";
        log_file << nearest_angle_90_to_180 << ",";

        float x_90_to_180 = nearest_distance_90_to_180 * cos(nearest_angle_90_to_180 * M_PI / 180.0);
        float y_90_to_180 = nearest_distance_90_to_180 * sin(nearest_angle_90_to_180 * M_PI / 180.0);

        float sosial = forceSocial(nearest_distance_90_to_180, nearest_angle_90_to_180);
        float physical = forcePhysical(nearest_distance_90_to_180, nearest_angle_90_to_180);
        float force_static = sosial + physical;
        float angle_reverse = nearest_angle_90_to_180 * -1;

        //std::cout << "Obstacle kanan (degree): " << nearest_angle_90_to_180 << ", Distance: " << nearest_distance_90_to_180 << " m\n";

        distances.push_back(force_static);
        angles.push_back(angle_reverse);
    }
    else{
        log_file << "No Data"<< ",";
        log_file << "No Data"<< ",";
    }


    if (!distances_minus180_to_minus90.empty()) {
        float nearest_distance_minus180_to_minus90 = nearest_minus180_to_minus90->first;
        float nearest_angle_minus180_to_minus90 = nearest_minus180_to_minus90->second;
        log_file << nearest_distance_minus180_to_minus90 << ",";
        log_file << nearest_angle_minus180_to_minus90 << ",";

        float x_minus180_to_minus90 = nearest_distance_minus180_to_minus90 * cos(nearest_angle_minus180_to_minus90 * M_PI / 180.0);
        float y_minus180_to_minus90 = nearest_distance_minus180_to_minus90 * sin(nearest_angle_minus180_to_minus90 * M_PI / 180.0);

        float rsosial = forceSocial(nearest_distance_minus180_to_minus90, nearest_angle_minus180_to_minus90);
        float rphysical = forcePhysical(nearest_distance_minus180_to_minus90, nearest_angle_minus180_to_minus90);
        float r_force_static = rsosial + rphysical;
        float r_angle_reverse = nearest_angle_minus180_to_minus90 * -1;

        //std::cout << "Obstacle kiri (degree): " << nearest_angle_minus180_to_minus90 << ", Distance: " << nearest_distance_minus180_to_minus90 << " m\n";

        distances.push_back(r_force_static);
        angles.push_back(r_angle_reverse);
    }
    else{
        log_file << "No Data"<< ",";
        log_file << "No Data"<< ",";
    }
    log_file << "\n";


    //std::cout << "Dis : ";
    //for (float dist : distances) std::cout << dist << " ";
    //std::cout << "\nAng : ";
    //for (float ang : angles) std::cout << ang << " ";
    //std::cout << std::endl;

    std::vector<float> angle_array;
    for (float angle : angles) {
        angle_array.push_back(angle * M_PI / 180.0);
    }

    std::vector<float> Fx, Fy;
    for (size_t i = 0; i < distances.size(); ++i) {
        Fx.push_back(distances[i] * cos(angle_array[i]));
        Fy.push_back(distances[i] * sin(angle_array[i]));
    }

    float resultant_vector_x = std::accumulate(Fx.begin(), Fx.end(), 0.0);
    float resultant_vector_y = std::accumulate(Fy.begin(), Fy.end(), 0.0);
    resultant_magnitude = sqrt(resultant_vector_x * resultant_vector_x + resultant_vector_y * resultant_vector_y);
    resultant_angle = atan2(resultant_vector_y, resultant_vector_x);
    resultant_angle = resultant_angle * 180.0 / M_PI;

    //std::cout << "Fnav : " << resultant_magnitude << std::endl;
    //std::cout << "Rho : " << resultant_angle << std::endl;

    std_msgs::Float32 xmsg;
    xmsg.data = resultant_vector_x;
    vector_x_publisher.publish(xmsg);

    std_msgs::Float32 ymsg;
    ymsg.data = resultant_vector_y;
    vector_y_publisher.publish(ymsg);

    geometry_msgs::Point magnitude_msg;
    magnitude_msg.x = resultant_magnitude;
    magnitude_msg.y = 0;
    magnitude_msg.z = 0;
    magnitude_publisher.publish(magnitude_msg);

    geometry_msgs::Point direction_msg;
    direction_msg.x = resultant_angle;
    direction_msg.y = 0;
    direction_msg.z = 0;
    direction_publisher.publish(direction_msg);

    log_file.close();
}

int main(int argc, char **argv) {
    ros::init(argc, argv, "sfmtest");
    ros::NodeHandle n;

    magnitude_publisher = n.advertise<geometry_msgs::Point>("/fnav", 1000);
    direction_publisher = n.advertise<geometry_msgs::Point>("/rho", 1000);
    vector_y_publisher = n.advertise<std_msgs::Float32>("/resultant_vector_y", 1000);
    vector_x_publisher = n.advertise<std_msgs::Float32>("/resultant_vector_x", 1000);

    ros::Subscriber sub_distances_90_to_180 = n.subscribe("/distances_90_to_180", 1000, distances90to180Callback);
    ros::Subscriber sub_distances_minus180_to_minus90 = n.subscribe("/distances_minus180_to_minus90", 1000, distancesMinus180toMinus90Callback);
    ros::Subscriber sub_distances_minus150_to_150 = n.subscribe("/distances_minus150_to_150", 1000, distancesMinus150to150Callback);

    // Generate a unique filename based on the current time
    std::time_t now = std::time(nullptr);
    char buffer[80];
    std::strftime(buffer, sizeof(buffer), "topic_log_%Y%m%d_%H%M%S.csv", std::localtime(&now));
    filename = buffer;

    log_file.open(filename, std::ios::out);
    if (log_file.is_open()) {
        ROS_INFO("OPENNING: %s", filename.c_str());
        log_file << "Timestamp,Magnitude,Direction,Distances90to180,Angle90to180,Distances-180to-90,Angle-180to-90,Distances-150to150,Angle-150to150\n"; // Header
        log_file.close();
    } else {
        ROS_ERROR("Failed to create log file");
        return 1;
    }
    
    ros::Rate loop_rate(30);

    while (ros::ok()) {
        sfm();
        ros::spinOnce();
        loop_rate.sleep();
    }

    return 0;
}
