<launch>
  <arg name="namespace" default="/"/>
  <arg name="cmd_timeout" default="0.5"/>
  <arg name="world_name" default="$(find ackermann_vehicle_gazebo)/worlds/parking_lot.world"/>
  <arg name="rvizconfig" default="$(find my_robot)/rviz/urdf_config.rviz" />

  <!-- Vehicle pose -->
  <arg name="x" default="-2.0"/>
  <arg name="y" default="-2.0"/>
  <arg name="z" default="1.2"/>
  <arg name="roll" default="0.0"/>
  <arg name="pitch" default="0.0"/>
  <arg name="yaw" default="0.0"/>

  <include file="$(find ackermann_vehicle_description)/launch/ackermann_vehicle.launch">
    <arg name="namespace" value="$(arg namespace)"/>
  </include>

  <group ns="$(arg namespace)">
    <!-- Create the world. -->
    <include file="$(find gazebo_ros)/launch/empty_world.launch">
      <arg name="world_name" value="$(arg world_name)"/>
    </include>

    <!-- Spawn the vehicle. -->
    <node name="spawn_vehicle" pkg="gazebo_ros" type="spawn_model"
          args="-urdf -param robot_description -model ackermann_vehicle
                -gazebo_namespace /$(arg namespace)/gazebo
                -x $(arg x) -y $(arg y) -z $(arg z)
                -R $(arg roll) -P $(arg pitch) -Y $(arg yaw)"/>

    <node name="controller_spawner" pkg="controller_manager" type="spawner"
          args="$(find ackermann_vehicle_gazebo)/config/em_3905_joint_ctrlr_params.yaml"/>

    <node name="ackermann_controller" pkg="ackermann_vehicle_gazebo"
          type="ackermann_controller">
      <param name="cmd_timeout" value="$(arg cmd_timeout)"/>
      <rosparam file="$(find ackermann_vehicle_gazebo)/config/em_3905_ackermann_ctrlr_params.yaml" command="load"/>
    </node>

    <node name="rviz" pkg="rviz" type="rviz" args="-d $(arg rvizconfig)" required="true" />

    <node name="sfm" pkg="ackerman" type="sfm" output="screen">
    </node>

    <node name="lidar_scan_node" pkg="ackerman" type="lidar_scan_node" output="screen">
    </node>

    <node pkg="ackerman" type="drive" name="drive" output="screen" launch-prefix="bash -c 'sleep 3; $0 $@'">
    </node>

    <node pkg="ackerman" type="keyboard_handler" name="keyboard_handler" output="screen">
    </node>

    <node pkg="ackerman" type="encoder_read" name="encoder_read" output="screen">
    </node>

    <node pkg="ackerman" type="fuzzy" name="fuzzy" output="screen">
    </node>

  </group>
</launch>
