<!-- filepath: /home/<USER>/catkin_ws/src/ackerman/launch/ackerman_gazebo.launch -->
<launch>
  <!-- Start Gazebo with an empty world -->
  <include file="$(find gazebo_ros)/launch/empty_world.launch" />

  <!-- Spawn the robot in Gazebo -->
  <param name="robot_description" command="$(find xacro)/xacro $(find ackerman)/urdf/ackerman_robot.urdf.xacro" />
  <node name="spawn_urdf" pkg="gazebo_ros" type="spawn_model" output="screen"
        args="-param robot_description -urdf -model ackerman_robot -x 0 -y 0 -z 0.1" />

  <!-- Start your C++ nodes (adapt as needed) -->
  <node pkg="ackerman" type="drive" name="drive" output="screen" />
  <node pkg="ackerman" type="keyboard_handler" name="keyboard_handler" output="screen" />
  <node pkg="ackerman" type="encoder_read" name="encoder_read" output="screen" />
  <node pkg="ackerman" type="sfm" name="sfm" output="screen" />
  <node pkg="ackerman" type="lidar_scan_node" name="lidar_scan_node" output="screen" />
  <!-- Add more nodes as needed -->
</launch>