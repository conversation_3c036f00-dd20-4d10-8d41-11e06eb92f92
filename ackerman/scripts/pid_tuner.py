#!/usr/bin/env python3

import rospy
from std_msgs.msg import Float64
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import threading

class PIDPlotter:
    def __init__(self):
        rospy.init_node('pid_plotter', anonymous=True)

        self.data_lock = threading.Lock()
        # Data storage
        self.start_time = rospy.Time.now().to_sec()
        self.setpoint_times = []
        self.setpoint_values = []
        self.actual_times = []
        self.actual_values = []

        # Time window for scrolling (in seconds)
        self.time_window = 10  # ←←← Add this line

        # Plot setup
        self.fig, self.ax = plt.subplots()
        self.setpoint_line, = self.ax.plot([], [], 'r-', label='Setpoint')
        self.actual_line, = self.ax.plot([], [], 'b-', label='Actual')
        self.ax.set_xlabel('Time (s)')
        self.ax.set_ylabel('Value')
        self.ax.set_title('PID Response')
        self.ax.legend()
        self.ax.grid(True)
        # Fix y-axis range
        self.ax.set_ylim(-1, 2)

        # Subscribers
        rospy.Subscriber("/velopub", Float64, self.setpoint_callback)
        rospy.Subscriber("/vehicle_speed", Float64, self.actual_callback)
        #rospy.Subscriber("/pid", Float64, self.actual_callback)

        # Animation
        self.ani = FuncAnimation(self.fig, self.update_plot, interval=0.1, blit=False)
        self.fig.canvas.mpl_connect('close_event', self.on_close)
        rospy.on_shutdown(self.shutdown)

    def setpoint_callback(self, msg):
        current_time = rospy.Time.now().to_sec() - self.start_time
        with self.data_lock:
            self.setpoint_times.append(current_time)
            self.setpoint_values.append(msg.data)

    def actual_callback(self, msg):
        current_time = rospy.Time.now().to_sec() - self.start_time
        with self.data_lock:
            self.actual_times.append(current_time)
            self.actual_values.append(msg.data)

    def update_plot(self, frame):
        with self.data_lock:
            sp_times = list(self.setpoint_times)
            sp_values = list(self.setpoint_values)
            act_times = list(self.actual_times)
            act_values = list(self.actual_values)

            # Optional: Trim old data
            # cutoff = rospy.Time.now().to_sec() - self.start_time - self.time_window
            # sp_times, sp_values = zip(*[(t, v) for t, v in zip(sp_times, sp_values) if t >= cutoff])
            # act_times, act_values = zip(*[(t, v) for t, v in zip(act_times, act_values) if t >= cutoff])

        self.setpoint_line.set_data(sp_times, sp_values)
        self.actual_line.set_data(act_times, act_values)

        current_time = rospy.Time.now().to_sec() - self.start_time
        self.ax.set_xlim(max(0, current_time - self.time_window), current_time)

        return self.setpoint_line, self.actual_line

    def on_close(self, event):
        rospy.signal_shutdown("Plot window closed")

    def shutdown(self):
        rospy.loginfo("Shutting down PID plotter node...")
        plt.close(self.fig)

    def run(self):
        plt.show()
        rospy.spin()

if __name__ == '__main__':
    try:
        plotter = PIDPlotter()
        plotter.run()
    except rospy.ROSInterruptException:
        pass