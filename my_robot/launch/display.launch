<?xml version="1.0"?>
<launch>
  <!-- Arguments -->
  <arg name="model" default="$(find my_robot)/urdf/my_robot.urdf.xacro"/>
  <arg name="rviz_config" default="$(find my_robot)/rviz/urdf_config.rviz" />
  <arg name="use_gui" default="true"/>

  <!-- Robot description -->
  <param name="robot_description" command="$(find xacro)/xacro $(arg model)" />
  
  <!-- Static transform from map to base_footprint -->
  <!-- This is for visualization purposes only -->
  <!-- <node pkg="tf" type="static_transform_publisher" name="map_to_base_footprint_broadcaster" args="0 0 0 0 0 0 map base_footprint 100" /> -->

  <!-- Robot state publisher -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"/>

  <!-- Joint state publisher -->
  <!-- Use the GUI version if the 'use_gui' argument is true -->
  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher" unless="$(arg use_gui)" />
  <node name="joint_state_publisher_gui" pkg="joint_state_publisher_gui" type="joint_state_publisher_gui" if="$(arg use_gui)"/>

  <!-- RViz -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(arg rviz_config)" required="true"/>

</launch>