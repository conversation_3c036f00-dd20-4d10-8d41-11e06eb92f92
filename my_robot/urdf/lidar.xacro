<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <xacro:property name="lidar_radius" value="0.05" />
    <xacro:property name="lidar_length" value="0.04" />

    <link name="lidar_link">
        <visual>
            <geometry>
                <cylinder radius="${lidar_radius}" length="${lidar_length}" />
            </geometry>
            <material name="grey" />
        </visual>
        <collision>
            <geometry>
                <cylinder radius="${lidar_radius}" length="${lidar_length}" />
            </geometry>
        </collision>
        <xacro:cylinder_inertia m="0.5" r="${2*lidar_radius}" h="${2*lidar_length}" 
                    xyz="0 0 0" rpy="0 0 0"/>
    </link>

    <joint name="base_lidar_joint" type="fixed">
        <parent link="base_link"/>
        <child link="lidar_link"/>
        <origin xyz="${base_length / 3.0} 0 ${base_height  + lidar_length / 2.0}" rpy="0 0 0"/>
    </joint>

    <gazebo reference="lidar_link">
        <material>Gazebo/Grey</material>
        <sensor type="ray" name="lidar">
            <updateRate>10</updateRate>
            <pose>0 0 0 0 0 0</pose>
            <visualize>true</visualize>
            <ray>
                <scan>
                    <horizontal>
                        <samples>360</samples>
                        <resolution>1</resolution>
                        <min_angle>-0.26166</min_angle> 
                        <max_angle>0.26166</max_angle> 
                    </horizontal>
                </scan>
                <range>
                    <min>0.1</min>
                    <max>10.0</max>
                    <resolution>0.01</resolution>
                </range>
            </ray>
            <plugin name="lidar_controller" filename="libgazebo_ros_laser.so">
                <topicName>scan</topicName>
                <frameName>lidar_link</frameName>
                <robotNamespace>/</robotNamespace>
                <updateRate>10</updateRate>
            </plugin>
        </sensor>
    </gazebo>
</robot>