<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    
    <material name="blue">
        <color rgba="0 0 0.5 1" />
    </material>

    <material name="grey">
        <color rgba="0.5 0.5 0.5 1" />
    </material>

    <material name="black">
        <color rgba="0 0 0 1" />
    </material>
    
    <xacro:macro name="box_inertia" params="m l w h xyz rpy">
        <inertial>
            <mass value="${m}" />
            <origin xyz="${xyz}" rpy="${rpy}" />
            <inertia ixx="${(1/12) * m * (h*h + l*l)}" ixy="0" ixz="0"
                     iyy="${(1/12) * m * (w*w + l*l)}" iyz="0"
                     izz="${(1/12) * m * (w*w + h*h)}"/>
        </inertial>
    </xacro:macro>
 
    <xacro:macro name="cylinder_inertia" params="m r h xyz rpy">
        <inertial>
            <origin xyz="${xyz}" rpy="${rpy}"/>
            <mass value="${m}"/>
            <inertia ixx="${m/12 * (3*(r*r) + h*h)}" ixy="0" ixz="0"
                     iyy="${m/12 * (3*(r*r) + h*h)}" iyz="0"
                     izz="${m/2 * r*r}"/>
        </inertial>
    </xacro:macro>

    <xacro:macro name="sphere_inertia" params="m r xyz rpy">
        <inertial>
            <origin xyz="${xyz}" rpy="${rpy}"/>
            <mass value="${m}"/>
            <inertia ixx="${2/5 * m * (r*r)}" ixy="0" ixz = "0"
                     iyy="${2/5 * m * (r*r)}" iyz="0"
                     izz="${2/5 * m * (r*r)}"/>
        </inertial>
    </xacro:macro>

</robot>