<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

    <xacro:property name="base_length" value="0.6" />
    <xacro:property name="base_width" value="0.4" />
    <xacro:property name="base_height" value="0.2" />
    <xacro:property name="wheel_radius" value="0.1" />
    <xacro:property name="wheel_length" value="0.05" />
    <xacro:property name="steering_joint_length" value="0.01" />
    <xacro:property name="steering_joint_width" value="0.01" />
    <xacro:property name="steering_joint_height" value="0.01" />

    <link name="base_footprint" />

    <link name="base_link">
        <visual>
            <geometry>
                <box size="${base_length} ${base_width} ${base_height}" />
            </geometry>
            <origin xyz="0 0 ${base_height / 2.0}" rpy="0 0 0" />
            <material name="blue" />
        </visual>
        <collision>
            <geometry>
                <box size="${base_length} ${base_width} ${base_height}" />
            </geometry>
            <origin xyz="0 0 ${base_height / 2.0}" rpy="0 0 0" />
        </collision>
        <xacro:box_inertia m="5.0" l="${2*base_length}" w="${2*base_width}" h="${2*base_height}" 
                    xyz="0 0 ${base_height / 2.0}" rpy="0 0 0"/>
    </link>

    <xacro:macro name="steering_joint_link" params="prefix">
        <link name="${prefix}_steering_joint_link">
            <visual>
                <geometry>
                    <box size="${steering_joint_length} ${steering_joint_width} ${steering_joint_height}" />
                </geometry>
                <origin xyz="0 0 0" rpy="0 0 0" />
                <material name="black" />
            </visual>
            <collision>
                <geometry>
                    <box size="${steering_joint_length} ${steering_joint_width} ${steering_joint_height}" />
                </geometry>
                <origin xyz="0 0 0" rpy="0 0 0" />
            </collision>
            <xacro:box_inertia m="0.5" l="${2*steering_joint_length}" w="${2*steering_joint_width}" h="${2*steering_joint_height}" 
                        xyz="0 0 ${base_height / 2.0}" rpy="0 0 0"/>
            
        </link>
    </xacro:macro>

    <xacro:macro name="wheel_link" params="prefix">
        <link name="${prefix}_wheel_link">
            <visual>
                <geometry>
                    <cylinder radius="${wheel_radius}" length="${wheel_length}" />
                </geometry>
                <origin xyz="0 0 0" rpy="${pi / 2.0} 0 0" />
                <material name="grey" />
            </visual>
            <collision>
                <geometry>
                    <cylinder radius="${wheel_radius}" length="${wheel_length}" />
                </geometry>
                <origin xyz="0 0 0" rpy="${pi / 2.0} 0 0" />
            </collision>
            <xacro:cylinder_inertia m="1.0" r="${2*wheel_radius}" h="${2*wheel_length}" 
                        xyz="0 0 0" rpy="0 0 0"/>
        </link>
    </xacro:macro>

    <!-- <xacro:wheel_link prefix="right" />
    <xacro:wheel_link prefix="left" /> -->

    <xacro:steering_joint_link prefix="front_right" />
    <xacro:steering_joint_link prefix="front_left" />
    <xacro:wheel_link prefix="right_back" />
    <xacro:wheel_link prefix="right_front" />
    <xacro:wheel_link prefix="left_back" />
    <xacro:wheel_link prefix="left_front" />

    <!-- <link name="caster_wheel_link">
        <visual>
            <geometry>
                <sphere radius="${wheel_radius / 2.0}" />
            </geometry>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <material name="grey" />
        </visual>
        <collision>
            <geometry>
                <sphere radius="${wheel_radius / 2.0}" />
            </geometry>
            <origin xyz="0 0 0" rpy="0 0 0" />
        </collision>
        <xacro:sphere_inertia m="0.5" r="${2*wheel_radius / 2.0}" 
                        xyz="0 0 0" rpy="0 0 0"/>
    </link> -->

    <joint name="base_joint" type="fixed">
        <parent link="base_footprint" />
        <child link="base_link" />
        <origin xyz="0 0 ${wheel_radius}" rpy="0 0 0"/>
    </joint>

    <!-- <joint name="base_right_wheel_joint" type="continuous">
        <parent link="base_link" />
        <child link="right_wheel_link" />
        <origin xyz="${-base_length / 4.0} ${-(base_width + wheel_length) / 2.0} 0" rpy="0 0 0" />
        <axis xyz="0 1 0" />
    </joint>

    <joint name="base_left_wheel_joint" type="continuous">
        <parent link="base_link" />
        <child link="left_wheel_link" />
        <origin xyz="${-base_length / 4.0} ${(base_width + wheel_length) / 2.0} 0" rpy="0 0 0" />
        <axis xyz="0 1 0" />
    </joint>

    <joint name="base_caster_wheel_joint" type="fixed">
        <parent link="base_link" />
        <child link="caster_wheel_link" />
        <origin xyz="${base_length / 3.0} 0 ${-wheel_radius / 2.0}" rpy="0 0 0" />
    </joint> -->

    <joint name="rear_right_wheel_joint" type="continuous">
        <parent link="base_link" />
        <child link="right_back_wheel_link" />
        <origin xyz="${-base_length / 4.0} ${-(base_width + wheel_length) / 2.0} 0" rpy="0 0 0" />
        <axis xyz="0 1 0" />
    </joint>

    <joint name="rear_left_wheel_joint" type="continuous">
        <parent link="base_link" />
        <child link="left_back_wheel_link" />
        <origin xyz="${-base_length / 4.0} ${(base_width + wheel_length) / 2.0} 0" rpy="0 0 0" />
        <axis xyz="0 1 0" />
    </joint>

    <joint name="front_left_steering_joint" type="revolute">
        <parent link="base_link" />
        <child link="front_left_steering_joint_link" />
        <origin xyz="${base_length / 4.0} ${(base_width + wheel_length) / 2.0} 0" rpy="0 0 0" />
        <axis xyz="0 0 1" />
        <limit lower="-0.785" upper="0.785" effort="10" velocity="1"/>
    </joint>

    <joint name="front_right_steering_joint" type="revolute">
        <parent link="base_link" />
        <child link="front_right_steering_joint_link" />
        <origin xyz="${base_length / 4.0} ${-(base_width + wheel_length) / 2.0} 0" rpy="0 0 0" />
        <axis xyz="0 0 1" />
        <limit lower="-0.785" upper="0.785" effort="10" velocity="1"/>
    </joint>

    <joint name="front_right_wheel_joint" type="continuous">
        <parent link="front_right_steering_joint_link" />
        <child link="right_front_wheel_link" />
        <origin xyz="0 0 0" rpy="0 0 0" />
        <axis xyz="0 1 0" />
    </joint>

    <joint name="front_left_wheel_joint" type="continuous">
        <parent link="front_left_steering_joint_link" />
        <child link="left_front_wheel_link" />
        <origin xyz="0 0 0" rpy="0 0 0" />
        <axis xyz="0 1 0" />
    </joint>

</robot>