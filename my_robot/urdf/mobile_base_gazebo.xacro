<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <!-- Import the properties from mobile_base.xacro -->
    <xacro:property name="wheel_separation_w" value="${base_width + wheel_length}" />
    <xacro:property name="chassis_length" value="${base_length}" />

    <gazebo reference ="base_link">
        <material>Gazebo/Blue</material>
    </gazebo>
    
    <gazebo reference ="right_wheel_link">
        <material>Gazebo/Grey</material>
    </gazebo>
    
    <gazebo reference ="left_wheel_link">
        <material>Gazebo/Grey</material>
    </gazebo>

    <gazebo reference ="caster_wheel_link">
        <material>Gazebo/Grey</material>
        <mu1>0.1</mu1>
        <mu2>0.1</mu2>
    </gazebo>

    <!-- <gazebo>
        <plugin name="diff_drive_controller" filename="libgazebo_ros_diff_drive.so">
            //update rate in Hz
            <updateRate>50</updateRate>

            //wheels
            <leftJoint>base_left_wheel_joint</leftJoint>
            <rightJoint>base_right_wheel_joint</rightJoint>

            //kinematics
            <wheelSeparation>0.45</wheelSeparation>
            <wheelDiameter>0.2</wheelDiameter>
            <wheelAcceleration>1.0</wheelAcceleration>
            <wheelTorque>20</wheelTorque>

            //Topics
            <commandTopic>cmd_vel</commandTopic>
            <odometryTopic>odom</odometryTopic>

            //Frames
            <odometryFrame>odom</odometryFrame>
            <robotBaseFrame>base_footprint</robotBaseFrame>

            //
            <publishOdomer>true</publishOdomer>
            <publishOdomTF>true</publishOdomTF>
            <publishWheelTF>true</publishWheelTF>
            <odometryTopic>odom</odometryTopic>
            <odometryFrame>odom</odometryFrame>
            <robotBaseFrame>base_footprint</robotBaseFrame>

        </plugin>
    </gazebo> -->

    <gazebo>
        <plugin name="gazebo_ros_ackermann_drive" filename="libgazebo_ros_ackermann_drive.so">
            <robotNamespace>/</robotNamespace>
            <commandTopic>cmd_vel</commandTopic>
            <odometryTopic>odom</odometryTopic>

            <front_left_joint>front_left_steering_joint</front_left_joint>
            <front_right_joint>front_right_steering_joint</front_right_joint>
            <rear_left_joint>rear_left_wheel_joint</rear_left_joint>
            <rear_right_joint>rear_right_wheel_joint</rear_right_joint>

            <wheelSeparation>${wheel_separation_w}</wheelSeparation>
            <wheelBase>${chassis_length}</wheelBase>
            <wheelDiameter>${2 * wheel_radius}</wheelDiameter>

            <maxSteer>0.785</maxSteer>
            <maxSpeed>5.0</maxSpeed>
            <maxAcceleration>1.0</maxAcceleration>

            <publishOdom>true</publishOdom>
            <publishWheelTF>true</publishWheelTF>
            <publishOdomTF>true</publishOdomTF>
            <odometryFrame>odom</odometryFrame>
            <robotBaseFrame>base_footprint</robotBaseFrame>
        </plugin>
    </gazebo>
    
</robot>
