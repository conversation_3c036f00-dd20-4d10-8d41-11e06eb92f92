<launch>
    <!-- Define arguments for file paths -->
    <arg name="model" default="$(find xacro)/xacro '$(find my_robot)/urdf/my_robot.urdf.xacro'"/>
    <arg name="rvizconfig" default="$(find my_robot)/rviz/urdf_config.rviz" />

    <!-- Load the URDF into the ROS Parameter Server -->
    <param name="robot_description" command="$(arg model)" />

    <!-- Add joint state publisher (non-GUI version) -->
    <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
        <param name="use_gui" value="true"/>
    </node>

    <!-- Robot state publisher -->
    <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"/>

    <!-- Include the Gazebo main launch file -->
    <include file="$(find gazebo_ros)/launch/empty_world.launch">
        <arg name="paused" value="false"/>
        <arg name="use_sim_time" value="true"/>
        <arg name="gui" value="true"/>
        <arg name="headless" value="false"/>
        <arg name="debug" value="false"/>
    </include>

    <!-- Spawn the robot model in Gazebo -->
    <node name="spawn_urdf" pkg="gazebo_ros" type="spawn_model"
          args="-param robot_description -urdf -model my_robot" />

    <!-- Start RViz with the specified configuration -->
    <node name="rviz" pkg="rviz" type="rviz" args="-d $(arg rvizconfig)" required="true" />

    <!-- TF library -->
    <!-- <node pkg="tf" type="static_transform_publisher" name="map_to_base_footprint_broadcaster" args="0 0 0 0 0 0 map base_footprint 100" /> -->

    <!-- Launch the robot control node
    <node name="drive_control" pkg="my_robot_bringup" type="drive_control.cpp" output="screen">
    </node> -->

    <!-- <node name="robot_control" pkg="my_robot_bringup" type="drive_control.py" output="screen">
        <param name="linear_speed" value="0.5"/>
        <param name="angular_speed" value="1.0"/>
    </node> -->

</launch>